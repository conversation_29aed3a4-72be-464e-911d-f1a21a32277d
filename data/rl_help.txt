Command Summary (@ is optional count, ~ is direction, ^R redraws, E<PERSON> aborts)
  c ~     Close a door              |   C       Character description
  d       Drop an item              | @ D ~     Disarm a trap/chest
  e       Equipment list            |   E       Eat some food
@ f ~     Force (bash) item or mons.|   F       Fill lamp with oil
  i       Inventory list            |   G       Gain new magic spells
  m       Magic spell casting       |   M       Map (shown reduced size)
@ o ~     Open a door/chest         |   P       Peruse a book
  p       Pray                      |   Q       Quit the game
  q       Quaff a potion            | @ R       Rest (count or *=restore)
  r       Read a scroll             | @ S ~     Spike a door
@ s       Search for traps or doors |   T       Take off an item
  t       Throw an item             |   V       View scores
  v       Version info and credits  |   W       Where: locate self
  w       Wear/Wield an item        |   X       Exchange weapon
  x ~     Examine surroundings      |   Z       Zap a staff
  z       Zap a wand                |   #       Search mode
  =       Set options               |   <       Go up an up-staircase
  /       Identify a character      |   >       Go down a down-staircase
@ CTRL-P  Previous message review   |   {       Inscribe an object
@ -  ~    Move without pickup       |   ?       View this page
@ CTRL  ~ Tunnel in a direction     |   CTRL-X  Save character and quit
@ SHIFT ~ Run in direction          | @ ~       For movement
Directions:     y  k  u
                h  .  l  [. to rest]
                b  j  n

To give a count to a command, type the number in digits, then the command.
A count of 0 defaults to a count of 99. Counts only work with some commands,
and will be terminated by the same things that end a rest or a run. In
particular, typing any character during the execution of a counted command
will terminate the command. Counted searches or tunnels will terminate on
success, or if you are attacked. A count with control-P will specify the
number of previous messages to display.

Control-R will redraw the screen whenever it is input, not only at command
level. Control commands may be entered with a single key stroke, or with two
key strokes by typing ^ and then a letter.

Type ESCAPE to abort the look command at any point.

Some commands will prompt for a spell, or an inventory item. Selection is
by an alphabetic character - entering a capital causes a desription to be
printed, and the selection may be aborted.

Typing `R*' will make you rest until both your mana and your hp reach their
maximum values.

Using repeat counts with the tunnel left command does not work well, because
tunnel left is the backspace character, and will delete the number you have
just typed in.  To avoid this, you can either enter ^H as two characters
(^ and then H), or you can type ' ' (i.e. the space character) after the
number at which point you will get a command prompt and backspace will
work correctly.

Umoria is free software, and you are welcome to distribute it
under certain conditions; for details, type control-V.

Umoria comes with ABSOLUTELY NO WARRANTY; for details,
type control-V and view sections 15-17 of the License.
