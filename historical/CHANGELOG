# Umoria Changelog 1987-2008


### 4.81 / 1987-4-30

Fixes before 1987-5-1

fixed moving into column one
tunnel through permanent rock prints tunnel through air? message
printing of 18/ stats
^C stops find flag, resting, searching
monsters bash doors with 0 hit points
what if pto_hit is negative?
test_hit, attack_blows, critical_blows are wrong
mon_tot_mult bug fixed
always have same stats when start
enchant armor scroll
wizard_create does not set level!!
should treasure level be set to finding level?
after cure blindness need a move_char(5) to restore light
lose_exp in spells.c
mass_genocide when there are no monsters
drop_throw, throwing object against wall goes into infinite loop
player titles for level wrong?
td_destroy should unlock chests
use of 8's in generate, set wallstk in build_tunnel
door/trap destroy should replace doors with corr2.floor, delete_object
when staff has 0 charges left, don't get warning message
recharge spell


### 4.81 / 1987-5-1

injection from types of wands to metals
extra space at end of scroll names
printing numbers in a field of 6
warning if wielding too heavy of a weapon
attack_blows, penalty for wielding heavy weapon too severe,
save routine, doesn't exit if you type ESCAPE, shouldn't if doing panic save
selling items to stores, they are still unidentified
item prices in stores line up on the right
rogue_like key binding option
spelling fixes
fixed getlogin() call in death.c
disable local special characters (^Y, ^R, ^W, ^V, ^O)


### 4.81 / 1987-5-2

fixed .<dir> problem caused above
fixed problem with speed, only call search_off in signal.c if search_flag set
food problems, fix initialization of py structure in variables.h


### 4.81 / 1987-5-2

after ^C, if type n, erase the line at top of screen
put refresh in erase_line(), so that lines really do get erased
monster 'o' Ogre Magi casting unknown spells
m_level problems, not calculating right numbers for monster generation
changed damroll/max_hd so that they no longer clear 'd' in strings
increased size of dtype for damroll strings
increased size of all defined strings, need one more char for the '\0'
wands of wonder, not generating new flag correctly
only print 'too heavy weapon' message if actually wielding weapon
wand of heal monster, causes them to scream when hit
put (void) before all sscanf calls
implemented shell_out and (no_)controlz functions
fixed build_tunnel, removed leftover fragment of earlier incorrect fix
	to build_tunnel
display_inventory doesn't clear last line when 12 items on one page 11 other
store_purchase when over 12 items in store passed calculated wrong
	count of number of items on second page
should put priv_switch stuff back in, i.e. setuid
game should be setuid


### 4.82 / 1987-5-3

fixed equip list bug when taking off items
changed shell escape command to '!'
changed version number to 4.82
restore terminal modes in signal.c before core dumping
fixed bug in save.c, it was writting/reading too much for magic_spell
refixed m_level code, m_level[0] must be added after the randint
wrote convert program to fix old save files


### 4.82 / 1987-5-4

put sigsetmask in signals.c, now dumps core on QUIT signal
fixed empty chests, set flags to zero so no treasure, if search
	then identify it as empty
missing break for amulets in magic_treasurein misc.c, caused cursed
	amulets to have positive attributes
dispell evil staffs gives segmentation fault, didn't check for muptr == 0


### 4.82 / 1987-5-5

your resist the affects -> you resist the effects
only print "%s appears healthier" if can see the monster
check all uses of muptr, make sure test it not zero before using
cost for amulets of searching is too high
evil iggy prints wierd messages when he hits, break missing


### 4.82 / 1987-5-6

fixed index bug in misc.c objdes(), used 0 as null string pointer
added sun to 'getuid' ifdefs in death.c and misc.c


### 4.82 / 1987-5-8

fixed help page for rogue mode, searching mode command is ^S
when stun monster by bashing, now does not print name if can't see monster
allow controlz again, except when setuid or accessing save files


### 4.82 / 1987-5-10

added break after cure_poison() in potions.c
items sold in stores do not appear in the inventory when last on second	page
signals.c should restore local special chars before core dump exit
monsters bashing doors end up on top of them
	can't hit the monsters, can't cast spells at them, etc.


### 4.82 / 1987-5-11

fixed cast_spell, cast was not initialized to FALSE
infravision in misc.c should be multiplied by 10
food items become known in a more reasonable fashion


### 4.82 / 1987-5-13

if cast from empty spell book, print error message
the base height/weight for female/male characters were reversed


### 4.83 / 1987-5-14

refixed empty spell book to distinguish the three cases, cas_spell
	now has three return values
removed the numbers (1 2 3 4 6 7 8 9) from get_dir prompt
changed some % chars to %% in print_monster() in files.c
changed HD from %d to %s in print_monster()
fixed jamdoor() was setting i_ptr->p1 instead of t_ptr->p1
redefine Search mode command for rogue_like option to #
changed message for rest_on, press '^C' not 'any key'
fixed potential infinite loop problem, must initialize curses before
	install signal handlers
extensive mods to the save routine, added protection mechanisms
changed version number to 4.83
minor fixes so that source passes lint, removed inkey_delay function
in store2.c, fix display_inventory() and purchase_haggle() so that
	items will not be listed with 0 price for high chr characters


### 4.83 / 1987-5-15

check result of fopen for writing scoreboard in top_twenty()
etc. misspelled in print_monsters()
added 'drools' and 'insults' messages in print_monsters()
more mods to save routine, compress cave before writing to save file
wrote wizard restore_char() function
fixed test for Nighttime in generate.c, should be turn/5000 not turn%5000
set moria_flag true after a call to restore_char() in moria.c
update game_version() to include info about me
ran through spell again


### 4.83 / 1987-5-16

should call prt_stat_block after wizard restore command
setuid code was just plain wrong, fixed it
----------------------------
Summer vacation!


### 4.83 / 1987-8-26

checked every switch statement, found five missing breaks


### 4.83 / 1987-8-28

changed use of geteuid() for random seeds, didn't work if setuid to root


### 4.83 / 1987-9-4 - 1987-9-6

port to SYS V
changed store type (and save file format) to avoid warning about nonportable
  signed character comparison


### 4.83 / 1987-9-9

added bug <NAME_EMAIL>, mainly dealing with setuid code


### 4.83 / 1987-9-11

correct spelling of receive_offer() (was recieve)
^R in store will 'really' clear screen now
get_haggle will not accept negative numbers now
disarm_all caused segment faults, missing level of braces after t_ptr = &...
more spelling errors: Delila(+h) Slim(-e)y Smok(-e)y


### 4.84 / 1987-9-16

took out old compatibility code in save.c
changed version number to 4.84
changed clear_screen and really_clear_screen so that they set msg_flag FALSE


### 4.84 / 1987-9-17

removed loop_flag = FALSE after prt_comment6(), stops wierd behavior if
  when buy/sell give a price greater/less than that offered
put entry in scoreboard when quit
modified get_char so that it will read 4.83 save files


### 4.84 / 1987-9-18

fix code so that it passes lint on ultrix and sun
fix curses bugs with Ultrix, by adding ifdefs for BUGGY_CURSES
  remove calls to nl(), and nonl(), don't write chars in last column


### 4.84 / 1987-9-19

port to SYSTEM V again
breakup all files so that they are less than 64K, except the doc file


### 4.84 / 1987-10-??

change all instances of (index(...) > 0) to (index(...) != 0)
fixed three errors, calling randint with arg of 0
fixed bug that allowed one to buy objects for negative gold pieces
fixed many other errors, but forgot to document them (sorry!)


### 4.85 / 1987-10-26

add comment about defining _DNO_SIGNED_CHARS for 3Bx series computers
SUN4 has variable class in math.h, added ifdefs to misc1.c and externs.h
  to avoid the conflict
make variables.h monsters.h treasure1.h and treasure2.h into c files
  to avoid problems compiling the HUGE main.c file on a 3B20
added an option '-o' to use the original style key bindings
added compiler option to Makefile, so that installer can choose
  which key binding style should be the default
control-c puts you in scoreboard only after character generation complete
changed all USG savetty/resetty to saveterm/resetterm, it still seems to work
changed version number to 4.85 for distribution purposes
  started mailing to comp.sources.games


### 4.85 / 1987-10-27

moved clear_screen in top_twenty to before the wizard1 test
in wizard_restore, don't exit if can't chmod the save file
when save file in wizard mode, leave it readable, this is necessary
  to allow the wiazrd to restore other player's 'touch'ed save files
fixed up the install option in the makefile, added comments to INSTALL
it is possible to get 'final offer = 0' when selling an object,
  fixed by making sure that min_buy and max_buy are >= 1


### 4.85 / 1987-10-29

fixed breath, was calling poison_gas with dam = 0
put note into MISC_NOTES on how to restore touched save files
finished mailing to comp.sources.games
  all bug fixes listed above are included in 4.85


### 4.85 / 1987-11-7

more fixes for SYSV shell out problem


### 4.85 / 1988-1-11

fixed "You are too confused to scanf..." message


### 4.85 / 1988-1-14

for SYS V, replaced rand() with lrand(), also fixed init_seeds, set_seed,
   reset_seed
fixed randint, it now returns 32 bits for both BSD & SYS V
replaced st_ctime with st_atime in save.c
deleted "? for help" messages during character creation
check_pswd now accepts CR or LF to end password, affected those who
  used BUGGY_CURSES option
when ^C to quit, now get "killed by quitting" message
fixed searching of trapped chests, printed wrong message
if control-C while resting, don't get asked if want to quit
added setgid(getgid) to main.c
remove extraneous refresh/putqio calls from io.c, greatly reduce CPU usage
casting blind monster on a monster that normally doesn't move,
  causes it to start moving, fixed
problem if control-C during -more- prompt fixed
after fork, close open file descriptors (scoreboard)


### 4.85 / 1988-1-15

after change of dex/str, misc stats are updated
checked all randint for possible randint(0) calls
remove bonuses for old object before adding bonus for new object in wear()
  this affected increase stat rings mostly
deleted unneccesary loop from restore_levels in spells.c
fixed 'no more' message for items that don't start with ampersand, in desc.c
fixed dprint in death.c, not printing last line of tombstone
fixed special effects (bolt, ball, breath) in spells.c by adding put_qio calls
locked chests were treated as trapped during searching in moria1.c
C command to file showed equipment list starting with b), files.c
when 21 items in inventory, equip list was not updated after removing an
   item,  unwear() in moria1.c


### 4.85 / 1988-1-18

fixed test of py.misc.fos in moria2.c, this number could go negative
in io.c, clear_screen cleared all used_line flags instead of just the ones
  actually cleared
in io.c, change loops that modify used_line to stop at 22, not 23


### 4.85 / 1988-1-22

in scrolls.c, tmp[] was accessed starting at index one instead of zero
in store2.c, when 12 items in store, offered to sell a-m instead of a-l


### 4.85 / 1988-2-6

added check of characters hit point in get_char, this ensures that dead
  characters will not be given a chance to restore their hit points
  dead characters can be saved by a signal
config.h was non-ansi, macro expanding inside of string constants not allowed


### 4.85 / 1988-2-7

fixed speed monster problem, infinite loop if point wand at wall


### 4.85 / 1988-2-12

compact_objects will no longer delete doors if are on the town level
in store_create, index to inven_init was off by one, this will fix store
	inventories
made init_seeds generate a more random initial setup
removed Public Domain messages, contradicted the 'all rights reserved' phrase
changed all msg_line, msg_line places to msg_line, 0
changed pause_exit in io.c, so that ESC does not quit game, this is
	inconsistent with rest of game, expecially get_char
quart_height, quart_width, msg_line changed to be constants
changed turn and store_open to be longs, before store_open was a short which
	meant that stores could not become locked after 64K turns
another spelling check of all files
fixed sell_haggle and purchase_haggle so that prices will not go the wrong
   way, i.e. no more negative numbers while selling
changed time check in save.c to 5 secs so that it matched sleeping time


### 4.85 / 1988-2-14

changed inner loop of print_map to use strcat instead of sprintf,
	this gives a big time savings here!!


### 4.85 / 1988-2-15

changed inner loop of printf_map to index a character pointer instead
	of using strcat
removed strcpy from lite_spot
delete unused code from rest check in dungeon.c, no longer refreshes screen
removed refresh from msg_print
eliminated most abs calls, speed up commonly called routines
speed up many of the most commonly used functions
eliminate test_light call in prt_map in misc1.c, inline the code
image avoided by typing ^R
changed rogue-like summon monster from ^U to ^S
fixed throw_object, did not draw object on screen as it moved


### 4.85 / 1988-2-18

fixed print_objects in files.c, had an exclamation mark before the get_string


### 4.85 / 1988-2-19

ooze bug?, cursor disappears, invulnerability, fixed by changing delete
	monster so that pushm() is ALWAYS called
no more silver bug, changed all object definitions so that 'number' is 1
player_save did not take level into account, two calls did not add it in
get_hitdie could return a negative number for increase in hit points
add rerolling of characters


### 4.85 / 1988-2-21

changed superb before excellent in likert() in misc1.c to be superb
  after excellent


### 4.85 / 1988-2-27

fixed some signal problems upon forking a subprocess
stone-to-mud that kills a creature now prints death message
added ' of' to name of infra-vision potion
can now enchant boots with enchant armor scroll
removed caps for 'Some' in treasure1.c
strip 'some' at start of string in objdes when pref is false


### 4.85 / 1988-3-5

always treat boots as armor, fixed scrolls.c, minus_ac in moria1.c
ask for confirmation if try to cast spell without enough mana
fix msg_print, put flush if -more- had been printed back in
fix control-c handler, add test for 0 in get_com of io.c


### 4.85 / 1988-3-7

many grammar/spelling errors fixed, complements Col Sicherman


### 4.85 / 1988-3-17

noticed that character rerolling has somehow disappeared?!?!?


### 4.85 / 1988-3-28

exit find mode if character takes damage (for any reason)
flush input, exit search/rest modes if creature attacks
broke up line in place_object, misc2.c to avoid reported compiler bug
fixed problem with range attacks vs filthy urchin, mon_take_hit returns
	-1 if no monster dies not 0 as before
if monster in unlit area lit by ranged attack then teleports, symbol not
	cleared, set in mon_spell_cast, set cast to TRUE when cast


### 4.85 / 1988-3-31

set_lightning_destroy was returning the inverse set
in generate.c, change "doorptr <= 100" to "doorptr < 100", so that
	array doorstk[100] accessed correctly


### 4.85 / 1988-4-6

off by one error in place_trap call in vault_trap in generate.c


### 4.85 / 1988-4-16

increase dam/increase ac rings were setting cost incorrectly in misc1.c


### 4.85 / 1988-5-1

salt water not clear POISONED message, call cure_poison instead of clearing
	poisoned flag
lose_exp takes off one too many mana points, wasn't rounding correctly
in death.c, print out 'mighty Queen' if female character
can 'kill' the program twice to get two save files, added var that only allows
	one signal handler to execute
fixed use of reset_flag for free moves, for many commands
cure blindness does not redraw monsters, added creatures(FALSE) call to
	dungeon.c
eliminate any abs that calls randnor (misc1.c), since abs may be defined
	as a macro
off by one bug for all player_exp references, could not reach level40,
con_adj() used before class adjustments to abilities made in create.c,
	so I moved it afterwards
bless prints max hit points, dungeon.c, the calls were deleted


### 4.86 / 1988-5-4

changed version number to 4.86, first diffs sent out


### 4.86 / 1988-5-6

fixed recharge in spells.c, did not redraw srceen properly


### 4.86 / 1988-5-7

in dungeon.c, moria1.c, moria2.c, scrolls.c, substituted INVEN_* constants
in dungeon.c, fast code called msg_print before slowing player, creative
    killing could make character permanently hasted


### 4.86 / 1988-5-10

after save character, set a global so that will not save again if
	catch a signal
exchange x command should report 'can not wield heavy weapon'
gain_level problem, could reach level 41 with ^J, change player_max_exp in
	main.c so that this won't happen, also changed player_exp[39] in var.c


### 4.86 / 1988-5-17

fix typos in misc2.c, io.c, dungeon.c, store2.c, moria2.c to get program
	to compile


### 4.86 / 1988-5-18

fixed typo 'of' -> 'or' in dungeon.c
another savefile/signal problem, set char_saved before sleep
more 'do action before calling msg_print' bugs in dungeon.c fixed, see 5/7
level 50 objects never created, make t_level 51 element array instead of 50
bashing effects (stunned) not cumulative


### 4.86 / 1988-5-20

added some SYS III notes to INSTALL
lamp fill messages added to moria2.c, to make it more informative
when player overeats, he is temporarily slowed
modified objects in treasure1.c and magic_treasure() in misc1.c
     so that all object pluses are part of name
changed subval of mush so that it is not same as slime mold
cast which spell? message does not go away if hit escape, fixed get_spell
erase_line now clear msg_flag if row == 0
now save stack of last ten messages, changes to io.c and dungeon.c
original commands changed, wiz pass ^P -> ^W, last message ^M -> ^P,
	create object ^W -> ^Q,   ^M did not work on Ultrix for last message
cloaks incorrectly priced, misc1.c was adding 100*to_ac to cost,
	this add is done in store1.c
treat boots like other armor, add to disenchant list in creature.c
turn over store contents every 1000 turns, if not on town level


### 4.86 / 1988-5-21

only print monster name if visible, moria2.c: throw_object()
	creature.c: confuse monster check in make_move()
	spells.c: sleep_monster1(), fire_bolt(), hp_monster(), drain_life(),
		speed_monster(), confuse_creature(), sleep_monster(),
		wall_to_mud(), poly_monster(), speed_monsters(),
		sleep_monsters2(), dispel_creature(), turn_undead(),
		lite_line()
cast which spell? prompt cleared when ask for list of spells, misc2.c
fix spells.c so that all spells return TRUE when they should
create food no longer destroys object under player


### 4.86 / 1988-5-22

add -cont.- message to store display, store2.c
``There is something there already'' *before* asking which drop item, moria2.c


### 4.86 / 1988-5-24

added panic_save, don't save scores for games from panic save files
    modified death.c save.c signals.c 
could 'ESC' from learn_spell without learning a spell, misc2.c
area_affect() moria1.c, exit immediately if clear find_flag


### 4.86 / 1988-5-25

change prt_field in misc2.c to only pad to 13 char, avoid Ultrix display prob
delete unnecessary space in prt_num in misc2.c, avoid Ultrix display prob
more mods to the repeat old message code
change create object command from ^Q to @ because of problems with control-flow


### 4.87 / 1988-5-26

don't flush keyboard buffer in create.c, inkey_flush no longer used anywhere
ifdefs SLOW around sleeping code, so that it is optional
allow much longer names to be entered to create command, wizard.c
clear store -more- message when number of items drop below 12
eliminate large number of uses of the strings " " and ""
looking at shop entrance was giving:
    you see no more The entrance to the magic shop..
changed version number to 4.87
increased stack to 20 messages
fixed spacing of equipment list header in file_character(), files.c
stop run at edge of screen, changed get_panel in moria1.c
add store_maint() call when restore save file, one for every day the file
	is old


### 4.87 / 1988-5-27

fixed *enchant armor* so that it randomly picks an object to enchant, scrolls.c
fix town_gen() of generate.c so that stairs always put in same place
in msg_print() of io.c, clear find flag to terminate a run
made sure that documentation race/stat class/stat adjustments corresponded
	with the program
the save adjustments based on classes were all wrong
mages fos (i.e. perception) was too high (i.e. bad)
added range adjustments to stealth and perception, so that display of this
	abilities gave better results, misc2.c files.c


### 4.87 / 1988-5-28

added character rerolling again to create.c
fixed indentation of race and class choices in create.c
added zillions of register declarations
eliminated the three different Makefile, put all configurable stuff 
	in config.h


### 4.87 / 1988-6-1

when spell casters lose exp, they lose spells too quickly
	lose spell when: not high enough level, num spells more than int allows
	should lose spells when lose int/wis
brought all miscellaneous documentation files up to date
store_open and turn changed back to worlint/int for save file compatibility
	reasons
put new version on ucbarpa, and announced it on the net


### 4.87 / 1988-6-3

when removing item in wear() moria1.c, do it after inven_destroy, otherwise
	inventory may increase to 23 objects thereby overwritting wielded obj
add message about void/int declaration of signal() to signals.c


### 4.87 / 1988-6-4

added message about gcc needing -fwritable-strings to Makefile and INSTALL
reworked Makefile to make it easier to use


### 4.87 / 1988-6-6

fix check_pwsd in misc2.c, was strcpy 12 char (13 with \0) into 12 char array


### 4.87 / 1988-6-9

added separate SYS_III and SYS_V defines to config.h
added indirection to function call in spells.c, damage() -> (*damage)()
fixed initialization of damstring in gold_list, treasure2.c


### 4.87 / 1988-6-14

updated README and INSTALL documentation
new Flexnames sed file for 4.87 umoria, created by shortc
add refresh call to shell_out, move duplicate code in dungeon.c into shell_out
SYS_III compiler gives register allocation errors for *= and /= operators
   only 23 of them, so I removed them all


### 4.87 / 1988-6-27

treasure1.c: rings of weakness are now initially cursed
spells.c: cdefense & 0x80000000 should be cmove, correct comments
many files, check places where find_flag cleared and make sure move_char(5)
	is called if necessary, particularly msg_print caused a problem


### 4.87 / 1988-6-28

Makefile: updated dependency list for .c files
many files: change move_char(5) to move_light(char_row, char_col, char_row,
	char_col), avoid problems with potential recursive calls to move_char()


# Working on:

Cheating/Tricks:
should potion stat increase (in_statp) be based on cstr (including ring plus)
	or on the characters base str (without ring plus)
  presently cstr which allows cheating by manipulation of cstr
  should de_statp be changed also?
should de_statt followed by in_statt increase stat to next 18/xx mod 10?
grape jelly trick, restore levels take you back to old hit point levels
  or give new levels based on current stats?
can circle monsters, by only using diagonals in a diamond shape

Things to be fixed:

check plurals "~" in object names, esp. Pint in treasure2.c
make get_panel run stops user customizable?
objects in outside corners do not stop run
moria1.c: area_affect does not need to check "if (find_flag)"
carefully check everyplace that clears find_flag, there are a lot
	of unnecessary checks, perhaps even some recursive calls to move_char
check setting of moria_flag in dungeon.c, esp. after restore_char ()
add "moria ^file" option to automatically do wizard restore,
   do the umask calls in the program instead of forcing user to type them
don't ask for quit on char '\0', should instead ignore it
check for EOF when reading characters
complaint that a hangup (kill -1) results in a game whose
	score will no longer be posted to the scoreboard
resting should have small but finite chance of waking a monster
  perhaps check once every 10 turns against randint(6) > stealth
cursor should be positioned somewhere before each inkey call
all npc spells should print descriptive message
allow rerolling after choose race/class
remove "clear" from random potion names?
pad should take a char not a string
print a number with the repeat_msg command
Message line overextending into second line should be erased
identify staffs/wands as having zero charges, when they run out
``can't carry that much weight'' before ``that many items'' is wrong?
   I like it the way it is now, though
still too slow on i/o, perhaps rewrite to avoid refresh() calls?
	perhaps use only motion optimization stuff?
Dot tunneling command
encrypt info before writing it to the save file
   perhaps xor with stat buffer
dprint in death.c is stupid!!
prt_map in misc1.c is stupid!!
draw_block and sub2_move_light is pretty stupid also, in moria1.c!!
problems reported with many calls to detect_sdoors, I think this is a result
	of the vault_trap bug fixed 4/6
subval, missile_ctr could be short ints
note that some armor can decrease to-hit values in docs,
	should these be displayed in name?
missing amulets, 1-4 strength, constitution, intelligence, dexterity
missing rings 5-6 gain wisdom, gain charisma
	note that missing rings/amulets are disjoint
missing staff 9 genocide
missing staff 24 mass_genocide
high level offensive spells give have a far lower damage/mana ratio than
	magic missiles
how to abort run on systems without ^C?
	perhaps some ifdefed code which does no-blocking I/O for them?
add death due to starvation
wielding heavy wepaons, fix + to hit on screen?
don't let dragons breathe on first attack, AMHD is nasty
	especially after the screen changes to the next panel
checking both ((!m_ptr->ml) or (!see_invis && mon invis)) is probably
	redundant when determining whether or not player can see a monster
list of spell books not cleared when get cast which spell? prompt
should we call m_name if spell only applies to visible creatures?
many spells do not print desciptive comment, aggravate_monster for instance
stores: haggling gets very boring
  add prompt to fix price of all objects with same starting price
  always go to minimum price after player has exhibited his excellence at
       haggling
when moving on objects, say what character moved onto (like rogue)
be able to move onto objects without picking them up (like rogue)
for throw command, ask for direction first (like rogue)
SYS V does not lock scoreboard when changing it
compressing save files would make many people happy
it is pretty easy to defeat the file protection mechanism, should I toughen
	it up?
if amulet raises stat over 118, stat will be lowered (more than it should be)
	when take amulet off, fix this by having 4 values for each stat
this file should be in reverse chronological order!
clonning shimmering molds, one droppped a chest, went over to search chest,
	panic save, get panic save everytime restore character, this
	may be related to old trap generation problem fixed 4/6
weight problem reported, character can not pick anything up?
what if can't write into current directory? (saves files, etc.)
should detect monster work on invisible creatures if have see invisible
	ability from ring/sword/etc.?
does not understand ~ in path names
loss of strength doesn't force you to drop items
in general, loss of an ability should have many affects, non of which are 
	implemented, for example, lose of intelligence should cause a mage
	to lose spells
cases of permanent slowness reported?
add user name to scoreboard?
also add points and experience to scoreboard
change cost in sell_haggle to a long
change item_value to return a long
change turn to a long
store_open should be a long
use of reset_flag for free moves still a little inconsistent
fix save files to include obj desc, then only need one random number
	generator which can use table program
replace magic numbers with constants
name objects if use did not identify them

add a pull-back map command like the PC version, display entire map on screen
   only showing important features, like stairs

Very Hard things to add:
recenter character on screen?
can't look in any direction, only in the 8 dirs
can't cast in any direction,

Long term stuff, enhancement suggestions:
give player something to do with money, i.e. 1Million gp for a HA +10 +10 sword
'flavor' the levels, dragons on one level, undead on another, etc.
what's been discovered list
use environment variables to specify rogue-like/original keys
use environment variable for default save file
add option to restore files from default save filename
commands not close enough to rogue style
fixed item lettering in inventory, in-line/cmd-line options?
scoreboard have all scores, not top twenty
command line option to print out times open
can't drop, identify, pick up, or throw  items in equipment list
Y destroy command, allow destroy from equipment list
let o, c, D commands automatically pick direction when they can
give rogue's a chance to steal, e.g. let monsters carry items that they
  pick up
give magic users a chance at pre identifying scrolls
could use a help system, like VMS version
make scroll of identify more common, to help non-MU character classes

Features:
hero/superhero do not subtract hit points when effect wears off
WOR scroll can save you with minus hit points!!
detect monster does not detect invisible monsters (normally)
can hit monsters in walls, but can not cast at them
can not enchant something with negative numbers (i.e. cursed items)
how does armor protect against breath/gas? it doesn't!!!
run stops one character before room lights, because it is supposed to


### 5.0 / 1989-2-26

all files: merged the umoria and PC-Moria sources
creature.c: get_moves(), 12 numbers were wrong for monster movement
wizard.c: a few more int to bigint_t changes needed in wizard mode commands
files.c: fix columns in monster dictionary so that everything lines up
misc2.c, moria2.c: line up columns for print_new_spells() and examine_book()
8 files: replace == with = in comments and strings
magic.c, misc1.c, misc2.c, moria2.c, signals.c: missing/misplaced breaks
monsters.c: 2 grey snakes, renamed one to green
misc1.c: compact_objects, stmt which decrements cur_dis in wrong place
spells.c: disarm_all should not set traps to zero, should just clear trap
	bits
moria2.c: in jamdoor() i_ptr->p1 should be t_ptr->p1
moria2.c: in place_trap(), covered pits should transform to open pits
moria2.c: mon_take_hit(), int i should be bigint_t i
io.c: more prompt will now accept space/escape/return/linefeed
io.c: EOF code did not work on machine with unsigned characters


### 5.0 / 1989-2-27

save.c: missing controlz calls on error returns
creature.c: don't let non-moving creature move if glyph prevented it
	from attacking
creature.c: give monsters a chance to recover from Bash, depends on
	square of monster level
moria2.c: give monsters a chance to ignore bash, function of hitpoints
	and level
spells.c: fixed restore_level, needed while loop
moria2.c: monster_multiply(), i=18 statement in wrong place, would not
	always multiply monster when it should


### 5.0 / 1989-3-2

moria1.c: get_panel(), add one to y and x on lines 945/950, prevent scrolling
	while inside rooms


### 5.0 / 1989-3-9

wizard.c: tmp_val = -999; for gold changed to tmp_lval
signals.c: added SIGXCPU to list of signals that cause panic save
spells.c: call monster_name before mon_take_hit in fire_bolt()
moria2.c: move_char (), stop before attacking a creature
dungeon.c: added move_char (5) to see_infra and see_invis setting and clearing


### 5.0 / 1989-3-10

creature.c: added take_hit (0, ddesc) line to every monster spell, ensure
	that player leaves resting/find/search modes
potions.c: remove chp += mhp code from gain const potion, now chp = mhp
moria2.c: change monster_death(), summon_object 4d2 instead of 4d3
	so that comments match code
creature.c: make_move(), prevent monster from moving again after opening
	a door, also, make chance of opening locked door dependant on monster
	level
monsters.c: giant red ant lion, not hurt by fire, move&attack normally


### 5.0 / 1989-5-5

generate.c: fixed error in generation of type 3 (subtype 3) rooms
misc2.c, config.c: added alternate wizard code/definitions
treasur1.c: restore mana potion made more valuable (35 -> 350)
	    staff of speed value increased   (800 -> 1000)
	    ring of sustain charisma level changed (7 -> 44)
	    ring of stupidity level decreased  (20 -> 7)
	    potion of gain strength value increased  (200 -> 300)
variable.c: halfling clan elder had 0% chance
	    gave warrior higher base to hit chance than paladin
store1.c: digging tools must be identified before price is adjusted
misc1.c:  do not adjust cost of digging tools based on value of p1
	  change all mods to fields toac/todam/tohit to be += or -=
	  several items (mostly cursed items) needed %P1 added to name
	  for digging tools, inc p1 instead of replacing it, so that tools
	     will still have same minimum values as specified in treasur1.c
monsters.c: Forest Wight should be able to open doors.
	    Grey Wraith should not drain mana
moria1.c: fix stealth so that it uses the value of p1
treasur2.c: change 'a's to '&'s for doors
spells.c: word of destruction should not put wall over character
	  Could not use wand of drain life at a distance
moria2.c: when in find mode, attack if run into creature only if can't see it


### 5.0 / 1989-5-7

moria1.c,moria2.c: remove heavy weapon message from test_hit and put it in
  py_attack instead
spells.c: teleport_away now calls unlite_spot if necessary, fixes bug that
    I thought had been fixed 3/28/88
save.c: for MSDOS: when restore save file, must search object list for secret
    doors/traps, and convert the '#/.' char to wallsym/floorsym, when save
    file, must do the reverse translation
spells.c: unlight_area() was really messed up, is much shorter now, will
	always print "darkness" message now if it should
misc1.c: cleaned up prt_map(), probably much faster now
moria1.c: cleaned up light_room(), draw_block(), sub2_move_light(),
	they are probably much faster now
all:  changed print(char *, int, int) to print(char, int, int) in io.c
misc1.c: crown of the magi, now has RL instead of see invisible


### 5.0 / 1989-5-8

all: checked uses of fm/pl/tl for consistency,eliminate extra parens/tests/etc
death.c: changed call to exit(1) to exit_game instead,
io.c, dungeon.c: new function move_cursor_relative to position cursor on map


### 5.0 / 1989-5-13

Moria.doc: added changes to bring it up-to-date, ran through Spell again


### 5.0 / 1989-5-18

spells.c: polymorph monster spell incorrect, missing an else flag = TRUE line
moria2.c: fix setting of reset_flag in find mode in move_char() routine
Moria.doc: created MacWrite version for Macintosh, plus some more editing
	corrections


### 5.0 / 1989-5-29

death.c, externs.h, files.c, io.c, misc2.c, moria2.c: eliminated the function
	pad, replaced most calls with printf %-30s formats
death.c: rewrote fill_str and renamed it to center_string, previously was
	returning pointer to local variable
death.c: eliminated dprint, replaced it with calls to put_buffer
signals.c: suspend handler now saves/restore special local chars (^R, ^W, etc)
generate.c: rewrote blank_cave, should be a bit faster now
externs.h, io.c, misc1.c, variable.c: eliminate used_line array, no longer
	call erase_line in prt_map in misc1.c
misc2.c, moria1.c, store2.c: make sure cursor is always in the right
	place after an inkey call
create.c, misc2.c: rewrite put_character and associated routines, line up
	columns, eliminate strcat/strcpy calls
files.c: rewrote file_character so that it matches put_character, etc.
io.c: fixed put_buffer so that it won't print past right edge of screen
files.c, misc1.c, moria1.c, ms_misc1.c: loc_symbol rewritten, now checks
	values of pl/tl/fm, did not correctly handle monsters which were
	visible, but were not in a lighted spot
moria1.c, io.c: rest() only erases MSG_LINE now if original 'hit ... to quit'
	message displayed, new function in io.c erase_non_msg does this
misc1.c: magic_treasure(), fixed helms which had wrong else level of cursed
	items, added cursed rings of searching, set p1 value for amulet of magi


### 5.0 / 1989-5-30

treasure1.c, treasure2.c: three axes changed from type 21 to 22, this prevents
	the Priest shop from buying these weapons


### 5.0 / 1989-6-2

creature.c, moria1.c, moria2.c, spells.c, types.h: massive reorganization
	of the way that visible monsters are handled, now ml is set if and
	only if the creature is visible on screen
creature.c, externs.h, moria2.c: procedure check_mon_lite removed, calls to
	it replaced by calls to update_mon ()
misc1.c: delete unused code
creature.c, moria2.c: two places needed to test fm in addition to pl and tl
spells.c: need to set fm/tl/pl before call to lite_spot/change_trap, eliminate
	some unncessary calls to lite_spot, eliminate unneccsary clearing of fm


### 5.0 / 1989-6-8

dungeon.c, moria1.c, moria2.c: allow searching while blind, decrease chance of
	successs if blind or have no light
moria1.c, moria2.c, store2.c: change prt("", x, y) to erase_line (x, y)
misc2.c: replace all "\0" with just ""
io.c, moria2.c, scrolls.c, store2.c: add '.' to end of sentences
dungeon.c, misc2.c, moria2.c, scrolls.c, store2.c: add msg_flag = FALSE after
	lots of msg_prints, to make sure never get " -more" message line
creature.c, dungeon.c, spells.c, staff.c: don't print 'light' message if blind
moria2.c: disarm_trap(), make no_light and blind same penalty, increase
	blind/no_light and confused penalties
moria1.c: rewrote area_affect, delete redundant code, don't stop at doorway
	if no_light and it is unlit
generate.c: fixed error in build_tunnel, stack points could overrun stack


### 5.0 / 1989-6-9

moria1.c: change test_hit so that always miss 1/20, and always hit 1/20
moria1.c, moria2.c, spells.c: changed fval incorrectly when manipulating
	doors, only change to corr_floor2 if next_to4 a room space
creature.c: in get_moves, change 1.732 to 2, makes it faster, also can no
	longer circle monsters by moving in a diamond shape
death.c, io.c, files.c, main.c: for MSDOS, don't leave the highscore_fd file
	open permanently
save.c: save value of total_winner, in case of a forced call to save_char()
all, types.h: deleted MSDOS ifdefs, the bytlint/byteint/wordint/worlint type
	definitions were confusing, so changed all to int8/int8u/int16u/int16
generate.c: rewrote loops to try to speed up level generation


### 5.0 / 1989-6-12

misc1.c: for items with duplicates in treasure1.c, set level of all equal
	to that of lowest
treasur2.c: several items did not match same item in treasure1.c


### 5.0 / 1989-6-13

treasur2.c: wands/staffs in store now have same level as those in dungeon
dungeon.c, misc2.c, moria1.c, ms_misc.c, store2.c: removed some ASCII
	dependencies


### 5.0 / 1989-6-16

externs.h, constants.c, generate.c, misc1.c: fixes to make level generation
	faster, rearrange order build_tunnel makes comparisons, call
	correct_dir directly instead of through rand_dir, rewrite loops,
	rewrite next_to4, rewrite next_to8
externs.h, files.c, misc1.c, moria1.c, ms_misc.c: loc_symbol returns char
== the following are due to CJS (Bruce Moria)
create.c: in get_money(), charisma was subtracted and added, now only add
desc.c: unquote() did not work at all, rewrote the whole thing
	in identify, string[0] == 'T' should be string[1]
moria1.c: area_affect, for direction 1, the 3 should be a 2
misc2.c, moria2.c, scrolls.c, spells.c, store2.c: change msg_print(" ") to ""
store2.c: eliminate erase_line/display_commands at end of purchase_haggle
config.h: for defined(Pyramid), define ultrix
eat.c: py.stats.intel to s_ptr->intel
creature.c:  in make_move(), set m_ptr at begining of function,
	  in mon_move(), set m_ptr at begining of function,
	test cmove before calling randint in mon_move()
	change if (randint(10) > py.misc.stl) to && (randint(...))
dungeon.c: move random teleport outside innermost loop, put with other checks
== end CJS fixes ==
creature.c: collapse a large number of if (...) if (...) constructions
	to if (... && ...)
constant.h, desc.c, externs.h, generate.c, misc1.c, rnd.c, save.c,variables.c:
	all versions now use rnd(), rnd does not use SYSV calling conventions
	anymore	BSD random() states eliminated
generate.c: added code to build_tunnel to prevent infinite loops


### 5.0 / 1989-6-19

*.c: collapse a large number of if (...) if (...) constructions
	to if (... && ...), delete unneeded braces


### 5.0 / 1989-6-21

creature.c, spells.c: build_wall and earthquake, now kill monster if it can
	not move through walls, and it can not move out of the way of the new
	wall, also, does more damage to those that escape, in creature.c,
	if in wall, must escape to empty space


### 5.0 / 1989-7-17

main.c: added a setbuf call, for buffered output


### 5.0 / 1989-8-4

merging changes due to Christopher J Stuart...zillions of changes
stat structure changed
externs.h: move functions definitions for lint to here
create.c: help functions sorta' added, money now more dependent on stats,
	  really good/bad characters eliminated, etc...


### 5.0 / 1989-8-8

creature.c: new function disturb used, stop attacks if player dead, add
	message for two attacks which had no message, confuse-monster only
	works if monster actually hits, door bashing different and noisy too!,
	creatures only eat lower level creatures, monster drawing changed,
	monster memory added, etc...


### 5.0 / 1989-8-9

desc.c: eliminate lots of unnecessary strcpy/strlen/etc calls, can inscribe
	objects, store/dungeon objects will be merged in the inventory when
	objects are identified, if curse a special object it loses its
	special ability, %d code for damage, %a code for armor, etc...
eat.c: misc changes...


### 5.0 / 1989-8-11

help.c: moved help text into files, added monster recall info, etc...


### 5.0 / 1989-8-14

Moria.doc: editing changes, removed lots of unnecessary hyphens
io.c: rebuilt it (accidentally deleted current version)
death.c: scoring procedures moved elsewhere, print_tomb more efficient and
	uses far less stack, final char display different, upon_death
	changed to exit_game, etc...
generate.c: added STATIC and void
magic.c: reduce indentation, increase spell damage, 7 dam/mana for Magic
	Missle, 4 d/m for balls, 5 d/m for bolts, etc...
misc1.c: crowns higher chance of magic, monster dist changed to increase
	chance of higher level monsters, unnecessary uid/eid calls deleted,
	m_level changed to include level[0] monsters, highlighting for ores,
	disappear percentages for objects in compact_obj() modified, '!'
	char added to magic descriptions, damage added to bows


### 5.0 / 1989-8-15

misc2.c: print message if gold/object created under player, stat handling
	completely changed, new func title_strings, search/rest/paralysis/count
	message print changed, new func prt_speed(), new stat functions, can
	hide stat changes if unknown, get_name uses loginname if none entered,
	change_name can print to file, inven drop/carry/etc changed to be
	a little simpler, new func join_names, spell_chance/learn_spell/etc
	chnaged to be a little simpler, get_spell with capital letter verifies
	choice, etc...


### 5.0 / 1989-8-16

monsters.c: no change
treasure1.c: %d added to weapons, %a added to armor, plus to dam added to
	bows, mage's guide should be mages' guide?? appostrophe removed


### 5.0 / 1989-8-17

treasure2.c: change & in traps to a/an, scroll/potion subvals changed to 200
	plus value in treasure1.c, etc...
potions.c: every potion prints message, ident true only if something noticable
	happens, greatly reduce indentation, stat handling different, etc...
prayer.c: reduce indentation greatly, use up mana if prayer fails, etc...
scrolls.c: reduce indentation, only ident if something noticable happens,
	for identify must search for new location of scroll, etc...
sets.c: ifdef out unused functions
dungeon.c: add command counts, use new func disturb(), double regen if
	searching, damage if really hungry, messages for protevil resist_heat
	and resist_cold, new code for quiting rest mode, move teleport code
	outside inner loop, add code to check strength/weight, find mode
	done differently now, allow ^char for control characters, all
	command chars translated to rogue_like chars, new commands = (options)
	{ (inscribe) V (view scores) M deleted but W improved : (map area)
	rogue-like summon ^S -> &, etc...


### 5.0 / 1989-8-24

files.c: init_scorefile deleted, intro->read_times, don't modify argv,
	new func helpfile, print_map deleted, print_monsters deleted,
	file_character now takes filename as argument, etc...
io.c: lint defs, new suspend function, much better curses handling,
	new func moriaterm and restore_term, inkey does refresh on ^R, flush
	should work now, new funct get_check, save_screen, restore_screen,
	etc...
constant.h: add defs for STATIC, ESCAPE, stats, increase treasure in
	streamers, change store min and turnaround amount, MAX_MON_NATTACK,
	MAX_EXP
wands.c: inscribe wand if empty, reduce indentation, etc...
staffs.c: inscribe staff if empty, reduce indentation, etc...
spells.c: identify- update stats if affected, allow ident of equip items;
	better light_line messages; when set pl false, must set fm false also;
	misc, etc...


### 5.0 / 1989-8-29

moria1.c: new function enchanted(), part of py_bonuses split into
	calc_bonuses, cur_char1/2 deleted, complete rewrite of inven_command
	routines, new functions check_weight/verify, can display weights,
	get_item can show either inven or equip, options added, run code
	completely rewritten, misc, etc...


### 5.0 / 1989-8-30

store1.c: item_value has *item->number removed, many routines changed to pass
	item pointer, identify removed from store_carry, known1/2 put in
	store_create, store_maint alg changed, etc...
store2.c: clean up handling of msg_flag, incremental haggling in get_haggle(),
	display_command moved from purchase/sell_haggle to store_purchase/sell,
	enter_store code cleaned up, etc...
config.h: misc...
externs.h: add new variables, delete old variables, rearrange stuff, etc...
types.h: added logging structure and recall structure, etc...
variables.c: add new variables, delete old variables, rearrange stuff,
	remove learn from spell structure, change titles so none shared
	between classes, etc...


### 5.0 / 1989-8-31

moria2.c: weight bug fixed, py_attack did not decrement inven_weight when
	inven_wield was a missile, and the player was carrying more than one
	of them


### 5.0 / 1989-9-1

moria2.c: carry has pickup option, new functions for inscribing objects,
	summon_objects returns type/number of objects created, ditto
	monster death, new function check_view, add todam for using bow/arrow,
	bashing changes, new sub py_bash extracted from old bash code,
	jamdoor p1 values changed successive spikes have smaller effect,
	etc...


### 5.0 / 1989-9-2

main.c: add bruce moria comment, new options, read environment variables, etc.
wizard.c: wizard can change weight and speed of character, etc...
unix.c: new file, contains UNIX specific code for user_name, check_input, and
	system()
signals.c: completely rewritten to be much nicer, same functionality
recall.c: new file, for printing out monster memory info
save.c: completely rewritten, same functionality, I think


### 5.0 / 1989-9-7

lint on mips machine


### 5.0 / 1989-9-8

lint on AIX (SYS V)


### 5.0 / 1989-9-9

lint on ultrix


### 5.0 / 1989-9-11

fix anonymous errors in order to get the program to compile...
io.c, ms_misc.c: put screen_map in io.c, and made it a default feature
signals.c, main.c, io.c: on EOF, the program now returns ESCAPE char until
	exits dungeon(), then saves with no penalty


### 5.0 / 1989-9-12

externs.h, desc.c, variables.c, types.h: mushrooms/colors/rocks/etc changed to
	char pointers instead of char arrays, saves space, make random init
	faster, change player title to char pointers also
moria1.c, potions.c, misc2.c, eat.c, dungeon.c, creature.c: cleanup handling
	of chp and cmana, call prt_c* only once every time they change
dungeon.c: set pointers only once at start of procedure
eat.c: reduce indentation
io.c, dungeon.c: remove msg_flag = FALSE line from inkey and put it in
	dungeon.c where get command


### 5.0 / 1989-9-14

creature.c: change put_qio calls to just setting screen_change to true
dungeon.c: remove superfluous erase_line and put_qio calls
many: make character numbers more clear, 'a' for 97, DELETE for 127, etc...
desc.c: objdes() bug with ins_buf handling, now clear ins_buf if none
many, moria1.c: made "Which dir?" default for get_dir()


### 5.0 / 1989-9-15

misc1.c, monsters.c, treasur[12].c, many others: changed all hit ponts from
	strings to a char array of size two, changed damroll to take two
	characters instead of a string, this eliminates most all scanf calls


### 5.0 / 1989-9-18

monsters.c, creature.c, types.h: replaced strings containing monster attacks
	with 4 byte array containing index into a monster attack table,
	this eliminates the rest of the scanf calls, and saves space
creature.c: many duplicate damroll calls in make_attack collapsed into
	a single damroll call
moria2.c: in chest_trap(), moved exploding chest trap to end to avoid
	dangling pointer problem; in twall, only light spot if c_ptr->tl
	is true;
wizard.c, recall.c, desc.c, files.c: changes to fix handling of new damage
	types for monsters and weapons


### 5.0 / 1989-9-19

many files: eliminated redundant trap_lista, fixed place_trap so that it
	no longer takes a typ parameter, change_type no longer calls
	place_trap, negative level values for traps all made positive
	and diasrm_object() changed appropriately
externs.h, dungeon.c, misc2.c, moria1.c, variables.c: eliminated print_stat
	variable since it was not used very much


### 5.0 / 1989-9-21

create.c, externs.h, variable.c: eliminated bit_array variable
variable.c: eliminated names of unused Rogue class spells
many files...: changed the floor/wall definitions from variables to constants,
	changed the values to make tests easier, all fval comparisons now use
	manifest constants, door type floors eliminated since new find/run
	code made them unnecessary
constant.h, misc2.c, externs.h, variable.c: changed var stat_column to
	constant, eliminated password variables
many files: changed moria_flag to new_level_flag, and reset_flag to
	free_turn_flag


### 5.0 / 1989-9-23

merged Xenix diffs and Atari ST diffs
treasure1.c: potion of sleep no longer cures blindness
wands.c: wand of wonder was wrong had 2 << randint() instead of 1 << randint()
eat.c, potions.c, scrolls.c, staffs.c, treasur2.c, wands.c: added store
	bought flag 0x80000000 to all food/potion/scroll/staff/wand objects
	in treasure2.c, modifed the code so that these objects do not give
	experience when used
all files: removed all floating point code except for randnor() and los()
many files: hp_player(-damage,"") calls did not work, change them all to
	direct calls to take_hit(damage),  hp_player string parameter
	removed since no longer used


### 5.0 / 1989-9-25

constant.h, config.h, externs.h, variable.c, misc1.c, misc2.c, potions.c:
	floating point randnor code replaced by integer on that uses a table,
	definition of MAXINT removed so there are now no differences between
	16 bit and 32 bit versions of moria, MAXSHORT and MAXLONG defined
Makefile, misc1.c, externs.h: calls to floor removed, math library no longer
	used!!, DONT_DEFINE_CLASS def for SUN4 no longer needed


### 5.0 / 1989-9-27

misc1.c: replaced los code with an integer arithmetic version by jnh (Joseph
	Hall), moria now uses no floating point numbers
dungeon.c, files.c, moria1.c, store2.c, wizard.c: removed all sscanf calls
	except for those in ms_misc.c, and one "%lx" in wizard.c


### 5.0 / 1989-9-28

treasure1.c, treasure2.c, types.h: change subval and number to 8 bit integers,
	move flags after name, move number before weight, save 12 bytes per
constants.h, etc: INVEN_MAX definition removed, INVEN_ARRAY_SIZE 1 smaller
variable.c: store_choice now array of 8 bit integers
monsters.c, treasure2.c: change t_level and m_level to array of 16 bit ints
many: interpretation of subval changed to fit it into byte, new range uses
	p1 to decide whether stacking permitted, torches can now stack
create.c, types.h: changed size of history from 400 to 240 characters,
	also print as lines of 60 instead of lines of 70
variable.c, misc2.c, types.h: changed definition of spell_type from 12 to 6
	bytes, new array spell_names to prevent duplicate strings,
	no longer have entry for warriors
variable.c, types.h: human female too light, made 150 from 120, also changed
	every base/mod field from 16 bits to 8 bits
types.h, variable.c: in background_type, change next and bonus fields to
	8 bit integers, added 50 to bonus to make it positive


### 5.0 / 1989-9-29

monsters.c: massive changes to monster file to fix inconsistencies


### 5.0 / 1989-9-30

set.c: make flasks of oil vulnerable to frost and fire, like potions
moria2.c: if set off a trap, temp set confused to zero to ensure that player
	will move onto the trap instead of in random direction
io.c, externs.h: confirm() no longer used, deleted
treasur1.c, treasur2.c: give non overlapping subvals to potions/scrolls,
	duplicate ones caused problems for giving random names
desc.c, treasur1.c: %M for mushrooms changed to %m
recall.c: fix printing of monster hit points, two numbers instead of a string
moria2.c: new look code from bruce moria, look in arcs so that can see
	anything on the screen, allows access to monster memories
generate.c, misc2.c, moria1.c, moria2.c: various fixes to reduce code size,
	increase speed, etc...
misc1.c, etc...: popm and popt no longer take a pointer, instead return int,
	pusht takes an int8u now instead of an int as a parameter


### 5.0 / 1989-10-1

all: added copyright notice to every *.[ch] file


### 5.0 / 1989-10-3

config.h, creature.c, recall.c, save.c: fixed a few bugs picked up by the
	ultrix compiler
dungeon.c: disabled the save command


### 5.0 / 1989-10-6

create.c, files.c, dungeon.c, main.c, save.c: wrote helpfile function,
	changed calling sequence so that caller does not have to do anything
constant.h, desc.c, variables.c: eliminated lots of excess colors/rocks/etc.
	eliminated all duplicates except between potions and mushrooms
types.h: fixed _frac problem with exp and chp, had to be unsigned shorts
misc1.c: made traps invisible again
moria1.c, moria2.c, store2.c: replaced strcpy(string, "constant") code with
	p = "constant" where possible
treasure1.c, treasure2.c: changed subvals for wands/staffs/amulets so that
	they start at zero, and don't have any missing numbers
spells.c: teleport_away() must clear m_ptr->ml before calling update_mon()
moria1.c, spells.c: check lite_spot() calls, eliminate unnecessary ones
death.c, misc2.c, wizard.c: eliminated lots of unnecessary blanks in string
	constants


### 5.0 / 1989-10-7

misc1.c, moria2.c, treasure1.c, treasure2.c: fixed lamp/torch subvals, again
	can not let torches have subval >= 64, the torch wield code will
	not work
store1.c: don't let items with subval >= 192 stack in stores, they are
	always handled as a group anyways
spells.c: fire_bolt and fire_ball fixed in regards creature lighting
variable.c: store_choice for temple wrong because broad aze renumbered
	earlier, changed 13-15 to 12-14
constant.h, dungeon.c, moria1.c: added print ac flag to status field,
	calc_bonus sets flag, prevent printing AC while in store
create.c: dis_ac must have dis_tac added to it
spells.c: detect_monster evil and insivible now set m_ptr->ml when
	print monster on screen, then creature() will correctly erase them


### 5.0 / 1989-10-8

types.h: fixed _frac problem with cmana, had to be unsigned short
moria1.c: in inven_command wear code, print 'too heavy' message after
	print wielding message
store1.c: item_value, must multiply value by item->number before returning
store1.c: sell_price return zero if item should not be put into store
monsters.c: disenchanter bat sleep set from 0 to 1
all: eliminated all unnecessary elipses
constants.h, creature.c, dungeon.c, eat.c: new defines for the creature
	move, spell, and defense fields, eliminated all hex constants from
	the 3 c files


### 5.0 / 1989-10-9

moria2.c: fixed falling rock trap in move_char(), step back so that
	player is not under the rubble
variable.c: put copyright message in string, so that it appears in executable
spells.c: create_trap modified to light up trap, in case it is an open pit
all:replace prt calls with put_buffer where ever possible, because it's faster
externs.h, io.c, signals.c: put ignore/default/restore_signal code back in
wizard.c: rewrote the version info
dungeon.c, moria2.c, signals.c: modified to use get_check() for consistency


### 5.0 / 1989-10-10

several spelling errors in CJS code
misc1.c: forgot to change magic_treasure code when changed subvals a couple
	of days ago for wands/staffs/amulets/rings
externs.h, files.c, misc2.c, types.h: wrote new stat code, all stat bugs
	fixed now, 6 stat values now, get_dis_stat() eliminated, new function
	modify_stat(),
misc2.c: get_obj_num changed, to increase probability of getting higher
	level objects
misc1.c, treasur1.c: had to change gain stat bits, the low 6 order bits of
	treasure flags, because changed the order earlier


### 5.0 / 1989-10-11

all: changed all 4 or less case switch statements except for one large one
	in generate.c to nested if statements, this is smaller and faster


### 5.0 / 1989-10-12

dungeon.c, generate.c, moria1.c: new_spot moved from dungeon to generate
	because alloc_mon and place_win_mon use character position, fixed
	minor new_spot bug
constants.c, misc2.c, dungeon.c: bst_stat() can not call prt_stat, changed
	to set py.flags.status, new stat change flags defined, and stat
	print code added to dungeon.c
creature.c: many duplicate disturb/strcat/msg_print brought outside the
	switch statement in mon_cast_spell
all: changed all unlite_spot() calls to lite_spot() calls, changed
	'if (test_light()) lite_spot()' code to just call lite_spot, this
	fixes many subtle bugs, mostly dealing with mon vis to infravision,
	unlite_spot code deleted
files.c, misc2.c: added stat_adj(A_INT) to srh which was wrong
misc2.c, spells.c, moria1.c, types.h: id_stat() and hid_stat[] eliminated
	no more hidden stat values, display arg dropped from bst_stat()
files.c, misc2.c: hex values replaced with constants


### 5.0 / 1989-10-14

constants.h, store1.c: increased store turn around from 4 (avg 2.5)
	to 9 (avg 5), changed store_main() to always call store create/destroy
	regardless of bounds, lowered values of bounds MIN and MAX


### 5.0 / 1989-10-18

moria2.c: when open chest, clear TR_CURSED flag, which just also happens to be
	monster win flag, to prevent easy wins
constants.h, misc1.c->wizard.c: replaced remaining hex magic numbers with
	manifest constants
dungeon.c: added code that gives a player a chance of noticing that items
	in inventory/equipment list are magical, the chance is much higher
	for warriors than for magi


### 5.0 / 1989-10-19

dungeon.c: ^P^P did not clear the line with the pause_line message
misc2.c: prt_state() did not clear all 3 digits of repeat counts
all: moved lots of procedures around to increase number of static fuctions,
	also made sure every C file less than 60K
create.c, extern.h, misc2.c, spells.c, variables.c: change way that mhp
	are calculated, new function calc_hitpoints(), new array player_hp[]
	which contains player mhp for each level
extern.h, main.c, misc2.c, potions.c, spells.c: change way that cmana are
	calculated, new function calc_mana(), called whenever gain level,
	lose level, or learn/forget spells
extern.h, main.c, misc2.c, spells.c: change the way that spells are learned
	and lost, learn_spell/learn_prayer deleted, new function calc_spells,
	new spell type state forgotten, new var spell_forgotten, etc.


### 5.0 / 1989-10-20

monsters.c: made spirit troll into 'G', made invisible, pickup, carrys obj,
	added drain wisdom attack, added drain intelligence attack to green
	glutton ghost
all: moved all msg_print(NULL) calls into io.c, specifically, clear_screen(),
	erase_line() and prt()
moria2.c, spells.c: two places that called test_light to see if monster or
	moving object lit were wrong, changed to eliminate check of fm
creature.c: update_mon() failed when hallucinating, changed print(cchar,...)
	to lite_spot() call
variables.c: adjusted call stat adjustments, mainly to make priest harder,
	gave priest negative str adjust, inc expfact from 10% to 20%
misc2.c: new funcs prt_int() and prt_long(), same as prt_num/prt_lnum()
	except no string argument, ": " moved into prt_num/prt_lnum string
spells.c: all wands now have limits on range
monsters.c: doubled Balrog eye sight from 20->40, evil iggy from 20->30
eat.c: increase hp gain for 22/23/24, increase hp loss for 27
constant.c, eat.c, potions.c, scrolls.c, staffs.c, treasur2.c wands.c:
	eliminate store bought flag, only gain exp when use an item if the
	use identifies the item


### 5.0 / 1989-10-23

magic.c, wands.c: spell/wand damage did not match
creature.c: monster stuck door bashing got sign of i_ptr->p1 wrong
recall.c: change corrosive gases to poison gases
externs.h, moria1.c: draw_block(), minmax(), maxmin() deleted, minmax and
	maxmin calls were unnecessary, draw_block only called from one
	place, sub2_move_light combined with sub1_move_light,
	sub4_move_light combined with sub3_move_light,
creature.c: fix mon_move() so that creatures that never move increment their
	r_attack[0] when standing next to the player, for Quylthulgs
spells.c: detect object and detect treasure set tl, which could also light up
	monsters, they now set fm


### 5.0 / 1989-10-24

spells.c: can not gain exp by disarming traps created by a scroll
types.h, magic.c, prayer.c, variable.c, misc2.c, constant.h: removed sname
	from spell_type since it was a trivial value, changed sexp from 16
	bits to 8 by dividing value by 4, saves about 310 bytes
many files: eliminated long stretches of blanks in strings, remove repeated
	chars from strings, typically " : ", saves over 1K of data size


### 5.0 / 1989-10-25

spells.c: aiming a ball type wand/spell at a 1-thick wall resulted in damage
	to creatures on the other side of the wall, now it doesn't
misc2.c: inc_stat changed so that player gains 1/6 to 1/3 of the distance
	from the max 18/100 stat, 0 to 100 takes 13 gain stat potions,
	dec_stat changed so that player loses 1/4 to 1/2 of the distance
	from the max 18/100 stat, 100 to 0 takes 13 lose stat potions
misc2.c: print_spells() modified so that spell are always listed with the
	same letter, i.e. the letter does not depend on whether or not
	you know the spells that precede it


### 5.0 / 1989-10-26

death.c: day string was size 11 should be 12
dungeon.c: make 'magik' detect same chance for each class, need to set
	i_ptr = &inventory[INVEN_LIGHT] before testing player_light,
	this could cause the light to go off permanently
misc2.c: calc_mana and calc_spells use p_prt->lev - class[].first_spell_lev-1
	instead of just the player lev
types.h, variable.c: add new field to class info, first level in which the
	player can learn a spell, used immediately above
variable.c: green glutton ghost no longer drains int
misc2.c: print 'welcome to level message' before learning spells


### 5.0 / 1989-10-28

config.h, externs.h, dungeon.c, wizard.c: version info put in a help file,
	game_version() function deleted
files.c: removed obsolete print_map() and print_monster() code
treasur1.c: made ring of WOE decrease wisdom, to balance ring of stupidity,
	removed trailing ^ secret symbols which made no sense, changed
	constant values from known2 to known1, such as ring of lordly protect
	armor class values
store1.c, store2.c, externs.h, types.h: add storenice feature by Dan Berstein,
	if (good bargain > 3 * bad bagains + 20) then always get final price
	in that store, two new functions, noneedtobargain() and updatebargain()
moria1.c: fixed wear command so that it prints inven letter of item removed,
	and equip letter of item worn, added extra param to remove()
generate.c, moria1.c, moria2.c, types.h: added an 'lr' flag to cave_type,
	this is set for every space in or next to a room, lr is true for
	lit rooms, and false for dark rooms, find_light() function deleted
	as it was no longer necessary, twall lights spots that have lr set
moria1.c, moria2.c: decrease search chances if hallucinating, decrease
	disarm chances if hallucinating, look fails completely
eat.c: print message "You feel drugged." for mushroom of hallucination
moria1.c: eliminate unnecessary in_bounds() calls
io.c: check to make sure tabs are all 8 character apart
treasur2.c: made object_ident array bss
misc2.c, moria1.c: fix calc_bonuses() so that it when wielding heavy bonuses,
	it subtracts the weight penalty from dis_th (display to hit),
	check_strength() modified to call calc_bonuses()
misc1.c: fixed compact_objects() and compact_monster() so that they update
	screen when deleting objects/monsters
creature.c, spells.c: put message in aggravate_monster(), took out redundant
	mesasge in creature.c
moria2.c: modify py_attack(), inven_throw() and py_bash() to make it more
	difficult to hit monsters which are not lit


### 5.0 / 1989-10-31

moria1.c: sub3_move_light() modified so that it does not print @ when in find
	mode
recall.c: had an 'i == 11 | i == 18' which should have been a ||
	printed out 11st,12nd,13rd levels incorrectly, now uses 'th'
moria1.c: fix see_wall, it assumed walls are # which is not true for PC/Atari
spells.c: breath() could do damage on the other side of walls, added an los()
	call to prevent this
moria1.c: area_affect treated 'potential corner' and 'branching side corridor'
	identically, which is wrong, added see_nothing() so that these could
	be handled separately


### 5.0 / 1989-11-1

moria1.c: when wearing object and must remove current object, fix code to
	use inven_check_num to see it is possible; in wear code, when remove
	old object check to see if inven_ctr increases before actually
	increasing wear_high
constants.h, misc2.c, moria1.c, store1.c, store2.c, treasure1.c, treasure2.c:
	change definition of subval 192 so it can be used for torches, it
	only stacks with others if have same p1 value, but it is always
	treated as a single object, change ITEM_* definitions in constant.h
	to match new definition, fix all code fragments that test subvals
desc.c: change to use new ITEM_ definition
moria1.c: fixed the potential corner/corridor intersection bug in the find
	code again, except this time it works better than the original code


### 5.0 / 1989-11-3

variables.c: decrease priestly HP bonus from 3 to 2
moria1.c: fixed wear code bug, called inven_check_num() with item not slot


### 5.0 / 1989-11-4

moria2.c: in tunnel(), print message if player tries to dig with hands
moria2.c: several places where object/treasure picked up did not clear
	c_ptr->fm, this is necessary since it might be set by detect spell


### 5.0 / 1989-11-7

moria1.c: fixed find_init so that moves straight into a corner would
	correctly run around the corner
moria1.c: changed the examine potential corner code in area_affect(), it
	would run into rooms if corridor entrance next to a wall, at least
	it doesn't do that for lighted rooms anymore


### 5.0 / 1989-11-8

prayer.c: in remove curse spell, only clear flag for items that are
	wielded or worn
io.c: truncate strings at col 79 instead of 80 if BUGGY_CURSES defined
moria1.c: print weights in show_inven and show_equip at col 71 not 72
io.c, misc1.c, moria1.c: change highlight mode for walls to display '%'
	instead, this is much more portable that original code that used
	standout mode and the high character bit, changed print(),loc_symbol(),
	and see_wall()
help.c: change help documentation for % character
wizard.c: modify wizard_create and change_character functions, add whitespace,
	exit if get_string fails (i.e. typing ESCAPE exits), modify get_string
	limits to more reasonable values, remove switch for setting tchar
moria_wiz_help, moria_owiz_help: correct errors in command list
death.c: add msg_print(NULL) after show_inven() in print_tomb() so that
	inventory can't	accidentally scroll of screen before exiting
creature.c, moria2.c, spells.c: can't call prt_experience() in mon_take_hit()
	as this gives "new level" message before "killed monster", move prt_exp
	calls to after msg_print at everyplace that calls mon_take_hit()
dungeon.c: modified repeat count code so that can edit the number entered
	leave cursor on msg line while entering repeat count
moria2.c: change summon_object() so that it will only create objects within
	los of point where monster dies


### 5.0 / 1989-11-9

store1.c: stacking code in store_check_num() and store_carry() did not
	handle torches correctly, changed to be the same as inven_check_num
	code in moria2.c
wizard.c: fixed mistake in change_character(), put *tmp_str != '\0' test
	in inside if statement, not outside one
externs.h, misc2.c, moria2.c: fix printing of spell characters from get_spell,
	print_spells changed consec TRUE to nonconsec == -1 for consecutive
	case, otherwise spells start with nonconsec=='a', added new arg to
	get_spells() first_spell which becomes nonconsec when call print_spell
wizard.c: move 'gold' from end of change_character to right after 'mana'
	since it is more frequently used than the rest
misc2.c: alloc_object(), fix it so that objects are not created underneath
	the player, this was a problem for rubble and traps
misc1.c: allow sling ammo to be enchanted in magic_treasure()
store1.c: fix search_list() call for ammo in item_value(), was passing 1 ?!?
	instead of correct subval
treasur2.c: subval for spike wrong, change from 192 to 193
misc1.c: remove see invisible from slay monster arrows, since it doesn't make
	sense
variable.c: change blows_table entry for STR/W .5-.7 and DEX 10-18 from one
	to two, the value of one made things too difficult for low level mages
monsters.c: for all monsters which used to be sleep value of 1 (old style),
	change their new style sleep values from 0 to 1
recall.c: stupid typo, change CM_CARRY_OBJ|CM_CARRY_OBJ to ..OBJ|CM_CARRY_GOLD


### 5.0 / 1989-11-10

moria2.c: disarm() had signs switched for trap chances, "tot - 100 + level"
	changed to "tot + 100 - level"
variable.c: change "tin plated" to "tin-plated" and etc.


### 5.0 / 1989-11-13

save.c: changes to get the save code working again
moria2.c: when move onto a trap, instead of restoring confused count, should
	increment it by the old value, since the trap may have set it
generate.c: for type 2 rooms, make them dark below level 25, not 30
moria2.c: when close a door, print "Something" instead of monster name if
	the monster blocking the door is invisible
moria2.c: in tunnel(), make the digging chance for non digging weapons equal
	to their max damage + tohit bonus instead of proportional to weight,
	the old code made lances better for digging than shovels
moria2.c: change py_bash() chance, make it a function of monster hp +
	monsters average max hp instead of level


### 5.0 / 1989-11-15

save.c, undef.c: restore_stats function eliminated, replaced by call to read
	py.stats from file, other fixes to get restore code working
creature.c: if creature never moves, kill it if it happens to be in a wall
	instead of moving it out of the wall
generate.c: everywhere that TMP1_WALL is used, must also set fopen FALSE
	so that place_object(), vault_monster() etc will work correctly
prayer.c: removed cure_blindness() and cure_confusion() from Holy Word spell,
	since could not pray while blind/confused
signals.c, io.c, externs.h: eliminate redundant definition of signal() in
	signals.c, also eliminate suspend_handler and assignment to it from
	signal(), make suspend() non-static
moria2.c: in look(), put blind and image tests before get_alldir()
moria2.c: in look() change [y to recall] to [(r)ecall]
creature.c, externs.h, generate.c, misc1.c, monsters.c, moria1.c, moria2.c,
	save.c, spells.c, types.h, constant.h: change m_list from linked list
	to a linear list, muptr deleted, monster_type nptr field deleted,
	function pushm deleted, now all scans of m_list go from mfptr (which
	always points at last element) to MON_MINIX == 2
misc1.c: compact_monster no longer calls prt_map(), which is unnecessary


### 5.0 / 1989-11-16

eat.c: fixed restore charisma mushroom, print 'stops' instead of 'starts'
moria2.c: fix calculation of avg_max_hp in py_bash(), didn't check CD_MAX_HP
monsters.c: spirit troll too hard to hit when can't be seen, lower AC 56->40
treasure2.c: fixed scare_monster trap, the subtype value was wrong 63->99,
	and the level needed 100 added to it
misc2.: fix get_spells(), must add first_spell to *sn instead of subtracting
	from spell[i]
creature.c: in mon_move, return immediately if creature is killed because it
	is in rock


### 5.0 / 1989-11-17

externs.h, spells.c, scrolls.c, prayer.c, staffs.c: change dispell_creature()
	to dispel_creature()
spells.c: dispel_creature uses m_ptr->ml after calling mon_take_hit() which
	is wrong, now save value before calling mon_take_hit()
moria2.c: turn find_flag into a counter, when it reaches 100, exit find mode
	with message "You stop running to catch your breath.", prevents
	infinite loop
generate.c: fixes to help prevent isolated rooms, in build_tunnel() make
	sure tunnel has gone at least 10 spaces from start before stop it,
	in cave_gen(), copy [yx]loc[0] to [yx]loc[k] so that can build tunnel
	from last room to first
spells.c: light_line() called lower_monster_name() which is wrong, change
	to monster_name() call
externs.h, creature.c, moria2.c, spells.c: removed seen argument from
	mon_take_hit, as it was used inconsistently, only get credit for a
	kill if monster is visible when killed
save.c, dungeon.c: call disturb in save_char() to turn off searching and
	resting, remove search_off call in dungeon.c as is no longer needed
variable.c: remove initialized data for py and old_msg since were (mostly)
	zero, set f_ptr->food and f_ptr->food_digested in main.c
constant.h: delete some unused constants, rearrange the order of a few others


### 5.0 / 1989-11-18

variables.c, externs.h, monsters.c, treasure2.c, save.c: every variable
	written to save file needs definate size, can not be just 'int',
	also spell_forgotten now read/written


### 5.0 / 1989-11-20

spells.c: sleep_monsters1() did not set NO_SLEEP info for monster memory
externs.h, moria1.c: changed new_spot to accept short pointers since
	char_row and char_col are now shorts
creatures.c, spells.c: multiply_monster(), removed slp parameter since it was
	always FALSE, added monptr, to fix the m_list bug
creatures.c: new m_list code fails when call delete monster from within
	creatures(), fixed it by changing some calls to delete_monster()
	to call delete_monster() only if the dead monster is between the
	current monster and mfptr, otherwise, call fix1_delete_monster()
externs.h, creatures.c, spells.c: breath() has new argument monptr, to fix
	the m_list bug
externs.h, creatures.c, spells.c: two new functions, fix1_delete_monster()
	and fix2_delete_monster() which together equal delete_monster,
	these are called from within creatures() when a monster is killed
many: eliminate the fopen field since I had 5 bitfields in the cave structure,
	and it is much easier to only save 4, added a BLOCKED_CORR case for
	secret/closed/locked/stuck doors and rubble, all code that used
	to test fopen now tests fval, most code that used to set fopen is
	now gone since it was redundant with fval setting code
moria2.c: fixed disarm() so that if there is a valid trap to disarm, it will
	print "Something/monster_name is in the way!" just like close door."
generate.c: place_stairs() could place them on the boundary, make sure
	that x/y can never be 0 or cur_xx-1, also, in town_gen(), call place_
	boundary() before place_stairs(), looks better, though not necessary
moria2.c: fixed openobject() so that if there is something to open, it will
	print an message just like closeobject if there is a monster in the way
save.c: fix another save file problem, noscorefile defined as int in get_char,
	just eliminated it and use noscore instead


### 5.0 / 1989-11-22

moria2.c: fix1_delete_monster() has to set m_ptr->hp to -1
creature.c: monster eating code checked monster exp wrong, was using cptr
	to index into the c_list
creature.c: in creatures(), call fix2_delete_monster if hp < 0
moria2.c, spells.c: dragon breath, wand bolts, wand balls, and thrown objects
	were visible even when blind, added checks, only print them if can see
moria2.c: tunnel(), print message "* is in the way" if there is a monster
	in the spot where the player is trying to tunnel


### 5.0 / 1989-11-23

save.c: player_hp was not saved/restored, total_winner and panic_save were not
	restored


### 5.0 / 1989-11-27

moria2.c: in tunnel, add the weapon damage to its digging ability
create.c: get_history(), clear the history strings before writing them with
	new values, to get rid of old strings
variable.c: for background strings, change "1st child" to "first child"
store2.c: add parameter to get_haggle() last_offer, in sell_haggle and
	purchase_haggle must set new_offer to 0, in get_haggle only allow
	incremental bid if new_offer == last_offer, i.e. at least one previous
	bid has been made
dungeon.c: in main loop, call creatures(FALSE) to light/unlight creatures
	when ever player_light value changes


### 5.0 / 1989-11-28

save.c: new save code, to reduce size of save file, and to make the savefiles
	portable
constant.h, desc.c, generate.c, misc1.c, misc2.c, moria2.c, save.c:
	changed the t_list from a linked list into a linear array of entries,
	just like the previous m_list change, new constant MIN_TRIX,
	all pusht calls changed to delete_object where ever appropriate, etc.


### 5.0 / 1989-11-29

misc1.c: increase p1 value for crowns of seeing by multiplying by 5


### 5.0 / 1989-12-4

creatures.c: change stealth code, instead of randint(10) < stl, it now cubes
	randint(64) and compares against 1 << (17 - stl), the result is that
	a change of one in stl always decreases monster notice chance by 80%,
	and perfect stealth is impossible
wizard.c: allow wizard to enter any stealth between -1 and 17
save.c: extensive changes to save code competed, save files are now xor
	encrypted, and are supposedly portable, all savefile protection code
	is gone


### 5.0.8 / 1989-12-5

save.c, undef.c, misc1.c: fix a few lint errors
changed version number to 5.0.8 and release the sources
creature.c: changed stealth code, amount monster woken by from 100 to 125
	(used to be 75 before 12/4)


### 5.0.8 / 1989-12-6

spells.c, creature.c: changed the way wall building wands work again,
	kill creature if it never moves, otherwise do a little damage,
	if creature can not move out of rock in mon_move, then do more damage
	and remove the wall ontop of the creature
various: check use of "?", especially in get_check() and verify() prompts,
	verify changed to replace the period with a question mark
spells.c, creature.c, misc1.c: breath(), update_mon(), and loc_symbol()
	changed to use blind status bit instead of blind counter, this
	helps make the display look better, i.e. nothing disappears until
	blindness takes affect in dungeon.c
monsters.c: more monster fixes from DJG
monsters.c, misc1.c, moria2.c, treasur1.c: change (SM) to (SA),
	slay monster to slay animal
monsters.c: set the new 'animal' bit correctly for all monsters
recall.c: change 'unnatural creature' to 'natural creature', change a few
	constants to defines
save.c: add error exits to every loop/array index in the get_char()
	routine to prevent crashes due to bad save files
store1.c: in item_value, set value to zero if known cursed, this prevents
	store from buying known cursed items


### 5.0.8 / 1989-12-7

store2.c: remove switches in prt_comment*() functions, replace with table
	look up to get proper string, saves much code space
creature.c: changed stealth code again, if resting/paralyzed monster has a
	chance of noticing once every 50 turns instead of never like before,
	change 64 to 1024, and 17 to 29 for a better distribution
constant.h, save.c: read noscore variable from savefile of dead character, now
	only read/write noscore for live characters, changed patch_level to
	9 so that old savefiles can still be read
save.c: in get_char(), the loop error prevention code was missing two gotos
store2.c: get_haggle(), when check for valid first use of incremental haggle,
	ignore leading spaces in the string
moria2.c: add_food() should increment slow value, not just set it when bloated
save.c: when can't create savefile, test for from_savefile before giving
	wizard message "Overwrite old?", when exit in wizard mode do the
	"readable to all?" check after closing the file
save.c: from_savefile set wrong, should always be set if have read from a file
	not just when a complete character is read
moria2.c: bash(), do not put character off balance if bash is successful
misc2.c: get_spell(), instead of beeping for unknown spell, print a message
recall.c: for monsters with sleep value of 0, could never learn that fact,
	change it so that this will be known if have killed 10 of them


### 5.0.8 / 1989-12-9

dungeon.c: call disturb when player takes poison damage, or starvation
	damage
variable.c: change the three doors from 'a ...' to '& ...', needed because
	of wall-to-mud wand
wizard.c: create_object wrong, was calling popt() then delete_object() then
	setting c_ptr->tptr, this fails because the delete objects will move
	the location (in t_list) of the just created object will change
treasur2.c: change 'nothing' item to be stackable, so that it can be safely
	picked up
save.c: was calling store_maint before objects were reidentified, this caused
	stores to end up with multple entries for stackable identified items
externs.h, misc2.c: new function inven_check_weight(), check toosee whether
	picking up an object will change the players speed
moria2.c: carry(), if picking up an object would slow you down, prompt with
	question "Exceed your weight limit?" to give player a choice
moria2.c, misc1.c: only show rock types in look() if highlight_seams option
	is set, also change option text from "highlight" to "HL and notice"
spells.c: in fire_bolt() and fire_ball(), set recall info if monster takes
	extra damage because of weakness or reduced damage because of breath,
	in dispel_creature() set EVIL/UNDEAD flag if creature hurt,
	in turn_undead() set UNDEAD if creature hurt, in drain_life() set
	CD_UNDEAD if creature not hurt
recall.c: "may carry one or two treasures" needs the plural, but the others
	remain singular/collective


### 5.0.8 / 1989-12-11

creature.c: stealth value changed again from 125 to 100
wizard.c, variable.c: change rogue base stealth from 4 to 5, let wizard set
	stealth to 18
monster.c: white worm mass sleep value from 10 to 1, crystal ooze 60 -> 1
creature.c: in drain mana spell, if r1 greater than cmana, then set r1
	equal to cmana to limit creature's hp gain
recall.c: when print out creature attacks, exit loop if i >= 4, caused
	problems for umber hulk, etc. which have 4 attacks
store1.c: instead of destroying all single stackable objects, only destroy
	one half on average (randint(number)), makes store 1 and 5 inventories
	look better, oil is much more common, identify a little more common
treasure2.c: accidentally change blank_treasure tval earlier, set tval
	back to 0, and set subval to 64
store1.c: change store_carry(), for group items, must update scost; for any
	stackable item, set ipos so that inventory will scroll to show it;
	let group items (except torches) stack over 24 since each group is
	created with a set number to begin with; ipos must be set to -1 at
	start not 0;
store2.c: store_sell(), pass a pointer to cur_top instead of just its value,
	if sold object not on the page shown, scroll to show the object


### 5.0.8 / 1989-12-13

recall.c: only print out known attacks, i.e. check r_attack, change i to count
	from 0 to 3, fixes bug with knowdamage(), add variable j to count
	attacks actually printed out
save.c: put limit on number of times store inventory changed in get_char()
store2.c: rewrite incremental haggle code again, add new parameter 'num_offer'
	to receive_offer, change get_offer parm last_offer to num_offer, only
	allow inc haggle if num_offer != 0; must reset new_offer after
	rejected offer in recieve_offer and sell/purchase_haggle


### 5.0.8 / 1989-12-14

misc2.c, lots: change way spell learning works, new command 'G'ain magic
	spells, new function gain_spells() in misc2.c formerly part of
	calc_spells(), new status flag PY_STUDY controls printing of 'Study'
	in status line, new field py.flags.new_spells number of spells can
	learn
origcmds.hlp, roglcmds.hlp: document the 'M'ap and 'G'ain magic commands
variables.c, potions.c: remove potion of learning as it is no longer useful


### 5.0.9 / 1990-1-1

creatures.c: in mon_cast_spell(), must update_mon() to light monsters before
	they cast a spell


### 5.0.9 / 1990-1-2

moria1.c: inven_command(), could not wear item which replaced equip item if
	inven full, only fail if inven_check_num fails for torches
misc2.c: change spacing for stats on change_character page, was printing in
	last column for max stat
moria2.c: tunnel(), if no shovel/pick, divide total tabil chance by two
monsters.c: change eye-sight and sleep values for consistency, as noted by
	djgrabin
help.c: change the monster recall so that it lists high level monsters first
creature.c: when monster bashes down a door, call disturb() to halt resting
io.c: fix msg_print() so that -more- is always printed on screen, fix
	put_buffer() so that does not fail if col argument greater than 79/80
misc1.c: change chance for ego weapons/missiles to 3*special/2 instead of
	just special, needed because treasure distribution change has made
	ego weapons not as common as before


### 5.0.9 / 1990-1-11

monsters.c: let nether wraith phase, set Black/Red/Multi ancient dragons
	to a sleep value of 700
creature.c: increase r_attacks if either notice true or r_attacks is
	nonzero (i.e. it has been noticed before)
moria2.c: move_char(), when step back after setting off a falling rock trap,
	check to see if have stepped back onto another trap
moria1.c: inven_command(), when restart it after taking off an item, don't
	print the 'have to drop something' message because that is confusing
misc2.c: modify_stat(), could overflow 118 when increasing stats, add extra
	case to enforce max of 118


### 5.0.10 / 1990-1-12

all: apply william setzer's objdes() diffs, 500K patch file, change version
	number to 5.0.10 and distribute
desc.c, misc2.c, store1.c: remove several obsolete uses of index(),
	insert_num() no longer used anywhere
constant.h, desc.c: change ID_TRIED,ID_KNOWN1 to OD_TRIED,OD_KNOWN1
desc.c: eliminate titles[], rename title_space[] to titles[]
all: fixed lint errors
all: make sure that all objdes() strings are bigvtype
desc.c: fix printing of bows/arrows/picks, needed new p1_use define, and
	rearrange code in objdes() switch statement
treasur1.c, treasur2.c: remove -100 tohit from books, was being printed, and
	is silly anyways
constant.h, externs.h, save.c, treasur2.c: change object_ident[] from
	MAX_OBJECTS to OBJECT_IDENT_SIZE = 7*64, see object_offset() in desc.c
	to see why this is necessary


### 5.0.10 / 1990-1-13

externs.h, misc2.c, store1.c: deleted obsolete references to treasure_type
desc.c: always print toac field if known2 and non-negative
config.h, io.c, misc1.c: eliminated BUGGY_CURSES define, caused too much
	trouble, just assume that can never print in last column of screen
constant.h, desc.c, misc1.c, moria2.c, spells.c, treasur1.c, types.h:
	created special_names table to hold all possible name2 strings,
	name2 changed to an int8u which holds index to special_names table
externs.h, main.c, treasure1.c: sort_objects() function deleted, sorting now
	done by init_t_level using O(n) bin sort, into sorted_objects[] array
files.c, misc2.c: object_list[] references that assumed it was sorted now
	indirect through sorted_objects[] array
main.c, variable.c: player_init indexes now point into object_list instead
	of inventory_init


### 5.0.10 / 1990-1-14

constant.h, externs.h, main.c, store1.c, treasur1.c, treasur2.c, variable.c:
	deleted inventory_init array, stores objects now in object_list,
	MAX_OBJECT adjusted, MAX_DUNGEON_OBJ defined, store_choice[] fixed
all: put all objects in object_list, trap_list, door_list, store_door,
	gold_list, up_stair, down_stair, rubble, much, blank_treasure,
	scare_monster are not separate variables anymore
all: invcopy now takes an object_list index as second parameter, name field
	deleted from inven_type and fields rearranged


### 5.0.11 / 1990-1-15

store1.c: search_list() deleted, use i_ptr->index instead to get raw item cost
save.c: obsolete treasure_type references deleted, save files work again
creature.c, magic.c, misc2.c, prayer.c, save.c, wands.c: fixed long/short
	problems with shift, mostly change (1 << i) to (1L << i)
all: add new register declaration, check indentation, fix 32/16bit int
	problems
constant.h: change version number to 5.0.11


### 5.0.11 / 1990-1-17

spells.c: aggravate was not initiallized in aggravate_monster()
monsters.c: novice rogue now evil like other rogues
misc2.c: 2 "... learn ... new spells ..." messages changed to print prayers
	instead for priests
moria2.c: drop_throw(), "The %s hits %s" changed to "The %s hits the %s."
many: fixed problems with setting of free_turn_flag


### 5.0.11 / 1990-1-19

misc2.c: fix inven_carry(), to stack store/dungeon items properly
desc.c: objdes(), set p1_use = IGNORED for food and potions, changed
	is_a_vowel(tmp_val[1]) to [2]
moria2.c: "exceed weight limit?" message now prints item name also
desc.c, spells.c, staffs.c, wands.c: empty wands are not incribed empty if
	known2, empty removed when wands are recharged
desc.c: put spaces between inscribed words
dungeon.c: always inscribe majik when character notices this, not just for
	inventory items as that helps priests more than fighters
store1.c: sell_price(), succeed only if both item->cost and item_value() are
	positive
creature.c: don't increase a number of attacks when monster repelled in
	make_attack(), i.e. no damage done so shouldn't learn about damage
moria1.c: regenhp and regenmana, could get chp/cmana equal to max and _frac
	non-zero, change c > max test to c >= max test
constant.h, dungeon.c, misc2.c: hp and mana were printed while in store/inv,
	fix calc_hitpoints() and calc_mana() to set flags instead of printing,
	in dungeon.c, checks flags and print values


### 5.0.12 / 1990-1-20

store2.c: redisplay store prices if charisma changes during the inven_command
	calls (actually just redraw everything)
moria2.c: monster_death(), dump must be set to 0 is number is 0
constant.h, moria2.c, recall.c, spells.c: recall printing of treasure changed,
	instead of setting CM_ bits, store largest number of treasure ever
	generated in those 5 CM_TREASURE bits, changed monster_death() to set
	the bits the new way, changed uses of monster_death() to replace old
	treasure bits with new ones, the vague print out treasure amounts
	replace with printout of precise number of treasures generated
externs.h, moria2.c: monster_death() changed to int32 monster_death()
moria1.c: new_spot() contained line of code "i = randint(10)" that did not
	belong there, was formerly testing code
desc.c: objdes(), force zero charges to always print, also, always print
	digging plusses even if zero
main.c: items that initialize players inventory are marked store_bought(),
	this is needed so that dagger/armor bonuses will be shown for them
desc.c: objdes(), fixed printing of doors, known1() clear the tried flag,
	magic_init() don't let scroll names end with a blank
misc2.c: gain_spells(), call calc_mana() if py.misc.mana == 0, this is
	for characters who did not know any spells before the 'G' command,
	can't gain spells if blind/no light/confused
moria1.c: infinite loop entered with 'id' and screen_change, also, 'n' to
	the continuing with inventory prompt did not work, must clear
	doing_inven before returning from inven_command() in these cases
desc.c: identify(), don't merge items in equip list, only merge items that
	are single stackable
misc2.c: inven_carry(), inven_check_num(), fix item stacking, items can only
	stack if they are both identified or both unknown (store items are
	always identified)
constant.h: change version number to 5.0.12 and distribute


### 5.0.12 / 1990-1-21

desc.c: objdes() bug, change p1_use = IGNORED to p1_use == IGNORED
moria2.c: move_char(), moving into a wall is no longer a free turn
misc2.c: gain_spells(), priests no longer need book before can learn spells
	from it, mages still need the books, calc_spells() delete unused
	var spells[], remove limit of 23 from number of new spells
dungeon.c: made scribe_object() a free turn again
recall.c: only print monster speed if at least one movement flag known
unix.c: missing */ after Gakk!! -CJS-.
desc.c: stacking did not work right, fixed known1_p() to return OD_KNOWN1
	instead of TRUE for store items, so that known1_p() == known1_p()
	will work consistently
monsters.c: creeping coins hit instead of bite, and touch instead of breath
variable.c: fixed problems with temple/alchemist store inventories
desc.c, store1.c: fixed food prices, only check unknown if subval puts it
	among the molds/mushrooms


### 5.0.13 / 1990-1-24

misc2.c, types.h: change inscrip back to char *, fix inscribe() to malloc
	space for string, and then copy inscription into the malloced space,
save.c: fixed (compatibly) to write/read new inscriptions
desc.c: free malloced inscription space in invcopy
death.c, misc1.c: delete max tcptr code
constant.h, desc.c, misc1.c: define new const ID_SHOW_TOHIT, in magic_treasure
	set this bit for every weapon,digging tool,missile,bow, in objdes()
	print out tohit/todam if this bit set and object identified, this
	makes it easy to tell ident weapons from unident weapons
main.c: char_inven_init() must set ID_SHOW_TOHIT flag for initial weapon
death.c, constant.h, misc1.c: change MAX_TALLOC from 225 to 150,
	delete temporary code which printed out max tcptr values
misc2.c: allow inscriptions of up to 24 characters
constant.h: change version number to 5.0.13 and distribute


### 5.0.13 / 1990-1-26

wizard.c: wizard_create(), initialize the name2 and inscrip fields to 0,
	init the ident field to known2 and storebought
constant.h: changed MAX_TITLES from 60 to 45
desc.c: magic_init(), wrote to a char *tmp; modified to write to a vtype
	string
recall.c: change printing of treasure again, only print 'sometimes' is 60% is
	only bit set, otherwise always print 'often' if only one object, for
	two objects, print 'often' only if 60% and 90% are only bits set
scrolls.c: recharge scrolls not used up when read, fixed setting of used_up
spells.c: res = TRUE; moved out of if-stmt so only needed once
treasure.c: change wiz obj name from "& wizard object" to "{wizard object}"


### 5.0.13 / 1990-1-29

moria2.c: tunnel(), give player attack on creature if it is in the way
misc2.c: gain_spell(), remove limit of 22 on learning spells, fix print_spells
	so that it will only show the first 22 spells
constant.h, desc.c, misc1.c: change ID_SHOW_TOHIT to ID_SHOW_HITDAM, set this
	bit for gauntlet/rings of slaying, and irritation/enveloping cloaks,
	fix objdes() so that it only prints both tohit/todam if this bit is set
	otherwise, only prints one number, if either of tohit/todam nonzero
desc.c, treasure.c: remove "the entrance to the " from store doors and put
	in objdes() in desc.c
misc2.c: put_misc2(), add "Exp to Adv." to character display
constant.h, desc.c, misc1.c: added defines for ID_NOSHOW_P1 and ID_SHOW_P1
	to improve printing of object names, objdes() always prints p1 if
	ID_SHOW_P1 set, and never prints it if NOSHOW set, these override
	the default p1_use setting, NOSHOW added to sustain stat rings,
	SHOW added to clumsy/weak gloves, boots of speed/stealth/slowness,
	helms of int/wis/infra/might/lordliness/magi/beauty/seeing/
	stupid/dull/weak/ugly, cloaks of stealth
creature.c: increase effect of aggravate_monster attack from range of 5 to 20
spells.c: dispel_creature(), prt_exp() was only called when visible creatures
	died, now always called when creature dies
misc1.c: added messages to compact_objects() and compact_monsters() so that
	can tell if they are ever called
store1.c: rings/amulets which are not known2 now have base value from object
	list instead of real value
moria2.c, staffs.c, wands.c: cast level to int, so that calculations will be
	done with signed arithmetic, in openobject() change chance of opening
	a chest, instead of -2*level use -level, this help eliminate chests
	which are impossible to open


### 5.0.13 / 1990-1-30

recall.c: print ' at normal speed' for monsters with mspeed = 1


### 5.0.14 / 1990-2-10

spells.c: turn_undead, speed_monsters, sleep_monsters2, and dispel_creature
	modified so that they only affect creatures in los of player
externs.h, misc2.c, variable.c: new array spell_order[], holds order in which
	spells are learned, in calc_spell() spells are now remembered in
	the order that they were learned, and forgotten in the reverse order
	learned, in gain_spell() must modify spell_order when spell learned
main.c: spell_order initialized in inven_char_init to all 99s
save.c: save/restore spell_order, for patch_level <= 13 savefiles create
	a reasonable spell_order on the fly
misc2.c: was erasing wrong line in gain_spells, change `j+2' to `j+1'
all: change file names to be 8.3 characters long
constant.h: change version number to 5.0.14 and release


### 5.0.14 / 1990-2-10

dungeon.c: change rogue-like wiz help key from ^? to \
all: merged in Mac sources, all initialized variables are now in variable.c,
	rearranged directory structure


### 5.0.15 / 1990-2-11

all: merged in VMS sources
changed to 5.0.15 and distribute


### 5.0.15 / 1990-2-13

moria2.c: carry(), printed 'worth of emeralds..' removed '.' from string
desc.c: crowns did not print out AC because it is zero, add special case
	to objdes() so that tval == 33 (crowns) always have AC printed out,
	change (+5 charges) to (5 charges)


### 5.0.16 / 1990-2-14

moria2.c: inven_throw() created second copy of pointer to malloced
	inscription, it now creates new inscription for new object
moria1.c: inven_command(), wear option copied malloced inscription,
	create new inscription for new object
misc2.c: inven_destroy(), free inscription of just deleted object,
	clear inscrip pointer of last objects previous location, so that
	it will not be freed, inven_drop(), create new inscription for
	new item, inven_carry() create new inscription for new item,
	scribe_object() free space for old insription since it is deleted
config.h, externs.h, misc2.c, moria1.c: rename remove() to takeoff() to
	avoid naming conflict with library routines
externs.h, misc2.c, store1.c: deleted join_names(), no longer doing anything
	useful, and was wrongly creating duplicate pointers to malloc blocks
moria2.c: make moving into walls a free turn again, but player can not attack
	invisible creatures in walls by moving into wall (hence preventing
	him from using 6 free turns to search for invis creature), must
	tunnel into wall instead, which always takes a turn
dungeon.c, externs.h, moria2.c: pass direction to tunnel() instead of point
	digging to, eliminates a lot of duplicate code, tunnel() now checks
	confusion, and tries to tunnel in random direction if confused
treasure.c: clone monster wands changed from level 2 to level 15, makes them
	recharge less, max 6 new charges instead of max 18 new charges
files/version.hlp: add Dan Bernstein's name to credits
magic.c, variable.c, wands.c: change stinking cloud from 16 to 12 damage as
	it was too close to lightning bolt at 18, reduce mana for mage/ranger
	from 4/6 to 3/5
moria1.c: show_inven() and show_equip(), don't print first two spaces when
	col is zero, since will blank the whole screen anyways
moria2.c: py_attack(), only cleared confuse_monster flag if monster survived
	attack, now always clear this flag and try to confuse monster


### 5.0.16 / 1990-2-16

all: add in diffs to get MSDOS version working again, add files from binary
	PC-Moria distribution
types.h: for PC, use 'unsigned char var : 1' to reduce sizeof cave_type
	from 5 to 4
desc.c, misc1.c, misc2.c, moria1.c, moria2.c, save.c, wizard.c, types.h:
	change inven_type inscrip field from pointer to an 13 char array,
	mallocing storage for inscriptions does not work, results in
	dangling pointers when inven_types are copied
save.c: add 'time' to savefiles, use this to calculate age of savefile
	instead of using stat, much more portable, and prevents cheating,
	if time is newer than current time, set age to 0, when save file
	check to see if current time greater than start time, if not,
	then save start time plus one day in save file
mac.c: delete getfileage since no longer used
recall.c: change knowdamage(), add damage parameter so that higher damage
	attacks take longer to learn than lower damage attacks
monsters.c: jellies and molds no longer have treasure
save.c: two places have (*str != NULL), changed to (*str != '\0')
save.c: remove support for pre 5.0.13 versions, rd_item() no longer needs
	patch_level
monsters.c: move crystal ooze from level 31 to level 40, to make raising
	crystal oozes for treasure much more dangerous


### 5.0.16 / 1990-2-19

all: added defines for tvals, and substituted every where in program


### 5.0.16 / 1990-2-21

ms_misc.c, io.c: remove all uses of mvprintw() for ibmpc port, since PC
	curses also pulls in the fp library if mvprintw is used
dungeon.c: in the recover from blindness code, the disturb() call must
	be before the creatures(FALSE) call
getch.c, io.c, Makefile: more fixes for the VMS port of umoria, new getch.c
	file, and new copy of (4.87) io.c file


### 5.0.17 / 1990-2-22

all: check again for numbers that should be constants
constant.h: change version number to 5.0.17
externs.h, main.c, moria2.c, signals.c, treasure.c: compile with gcc -Wall
	and fix all reported errors


### 5.0.17 / 1990-2-26

prayer.c: change (!class[].spell == PRIEST) to (class[].spell != PRIEST)
store2.c: for good bargaining, use final_ask not min_buy in sell_haggle()
dungeon.c: change "Sorry.." to "Sorry."
misc1.c: for gloves/gauntlets, p1 and ID_SHOW_P1 set in wrong place
creature.c: make_attack(), creature could be visible at start of attack but
	invisible at end due to a teleport, set notice/visible appropriately
	before monster attacks, and don't use m_ptr->ml afterwards
monsters.c: creeping coins always carry gold now, 1d2 for copper, 1d2+60
	for silver, and 1d2+90 for gold
treasure.c, wizard.c: change name of wizard items to empty string, and
	inscribe them with "wizard item"
desc.c: problem with rubble/stone-to-mud, change rubble case from like stairs
	to be like doors
moria1.c: show_equip(), change non-weight limit from 57 to 60, change weight
	limit from 49 to 52 chars, for show_inven(), change limits from
	66/74 to 68/76 characters
creature.c, magic.c, prayer.c, spells.c, wands.c: fix get_flags(),
	fire_ball(), fire_bolt() and breath() to use defined constants for
	attack types
moria1.c: show_inven(), show_equip(), make sure that col never less than zero
all: merged in Atari ST diffs from Stephen A. Jacobs


### 5.1.0 / 1990-2-27

creature.c: fixed bug with doors, always open/light door instead of only
	opening/lighting them when in los of player
moria2.c: when bash open door, only break it 50% of the time to be consistent
	with code in creature.c
all: delete some obsolete files, annotate a few others, put README files
	in the subdirectories to explain what is present
save.c: fix old savefile compatibility code to work with new version numbers
constants.h: change version number to 5.1.0  and publicly announce


### 5.1.0 / 1990-2-28

recall.c: creature is worth... sprintf() call used %ld instead of %d
moria2.c: look_ray(), every place GRADF multiplied by y, cast it to long
	to prevent overflow of 16 bit ints
ms_misc.c: were still two refs to 'byteint', changed to 'int8u'
MLINK.LNK: updated for umoria5.0, add undef and recall, change treasur[12]
	to treasure
create.c: delete sleep(1) call in character rerolling code
death.c: put "#ifndef L_SET" around the #define L_SET
io.c: ifdef out suspend code, for USG systems with BSDisms that have SIGTSTP
	defined but don't actually implement it
io.c: change name of variable 'border' to 'screen_border' to avoid naming
	conflict on SGI/SYS V machine
INSTALL: moved from doc subdirectory to root directory, and brought up to date


### 5.1.0 / 1990-3-1

misc2.c: two places where (1L << j) occurred, and j could be 99 (unknown
	spell from spell_order[]), this is an undefined operation, fixed
	source to not shift if j == 99


### 5.1.0 / 1990-3-3

misc1.c: set_options(), typing '-' on line 0 gave error, change i=max to
	i=max-1
store1.c: item_value(), calculating digging tool values wrong, subtract off
	objects initial p1 plusses before multiplying p1 by 100
dungeon.c: for paralysed/resting, move cursor to player before refresh call
creature.c: disenchanter bat attacks could put tohit/todam/toac below zero,
	fixed so that they can not go below zero
moria1.c: if have MAX_SHORT hp/mana, then could get overflow in regenhp/mana
	routines, added checks for overflow
dungeon.c, io.c: save on EOF code did not work, in inkey(), remove casts to
	char in eof test, remove call to feof(), in dungeon(), add !eof_flag
	to innermost while loop to ensure it exits on error
save.c: in sv_write(), clear death flag if eof_flag or panic_save set, so
	player can restart game to see tombstone
main.c: set death flag if hit points are negative, can happen if a HANGUP
	savefile or panic savefile is restored
save.c: if get_char(), don't overwrite died_from string if player is dead
main.c: if specify -r or -o, force rogue_like to appropriate value after
	read the savefile, since get_char() will modify rogue_like_commands
moria1.c: search(), "have found a trap.." fixed, removed extra period
monsters.c: added CD_NO_SLEEP flag to jellies, oozes, elementals and the
	gelatinous cube
dungeon.c: fix "which level" wizard prompt, limited to level 99 max


### 5.1.1 / 1990-3-4

all: merged in more Mac diffs, Mac version should compile now
wizard.c: stealth max 87 should be 18 in printed string
desc.c: identify(), if cursed incscribe with ID_DAMD, unsample(), do not
	clear the ID_DAMD flag, since an unsampled item is still cursed
config: change version number to 5.1.1 and release


### 5.1.1 / 1990-3-5

monsters.c: added CD_NO_SLEEP flag to creeping coins
recall.c: recharge(), change chance of failure from randint((num+40)/10)=1
	to randint((num+40-i_ptr->p1-i_ptr->level)/10)=1 to make it harder
	to recharge high level and highlly charged wands
recall.c: change j from int to int32u, since it is used with flag fields,
	change two %d uses of it to %ld
desc.c: objdes(), don't print ID_TRIED value for store bought items
moria2.c: a slow dart trap now has no effect if have free action
io.c: must ifdef out the tbuf.c_cc code, since this causes the program to
	fail for some reason


### 5.1.1 / 1990-3-13

signals.c: add void to USG signal_hander definition
externs, ms_misc.c: move fopen declaration from externs.h to ms_misc.c, and
	make static, and make FILE * not struct _iobuf
ms_ansi.c: include stdio.h
generate.c: change #if DEBUG to #ifdef DEBUG
config.h, generate.c, types.h: added ifdefs for IBMPC_TURBO_C


### 5.1.1 / 1990-3-16

moria1.c: inven_command() "Wear" prompt changed to "Wear/Wield"
monsters.c: earth elementals/spirits can now move through walls
spells.c: wall_build(), for earth elementals, increase their hit points
files.c: helpfile(), can ESC out now
spells.c: cloning a monster will wake it up now
dungeon.c: allow two-char control character sequences to be used with
	repeated commands (deleted else from "else if (command == '^')")
roglcmds.hlp: document how to avoid problem with repeated dig left command,
	i.e. '13^H' doesn't work, type ^ then H, or type SPACE then ^H


### 5.1.1 / 1990-3-19

unix.c: initialize num_chars to zero in check_input(), in case FIONREAD absent


### 5.1.1 / 1990-3-21

all: fix most SYS_V lint errors


### 5.1.1 / 1990-3-23

dungeon.c, io.c: calling inkey() when check_input true is wrong since it traps
	^R and does other stuff, instead call new function raw_inkey(), which
	only calls getch
io.c: flush(), occasionally caused loss of output, instead of using fancy
	ioctls, drain input by using check_input/raw_inkey


### 5.1.2 / 1990-3-25

dungeon.c, io.c, unix.c, atarist.c:  AIX can't check to see if input is
	pending, but can do non-blocking read, so check_input changed so
	that it consumes input when called
constant.h: change version number to 5.1.2 and distribute


### 5.1.2 / 1990-3-26

save.c: when resurrect, clear poisoned flag and food flag to prevent character
	from immediately dying again
main.c, misc2.c, unix.c, save.c: fix some BSD lint errors
moria1.c: remove unnecessary flush() call from take_hit()
creatures.c, misc1.c, externs.h, variable.c: fixed problem with calls to
	compact_monster() within creatures() by adding a horrible hack,
	new variable hack_monptr defined to hold current creature() monptr


### 5.1.2 / 1990-3-27

variable.c: add infra 3/2 for elf/half-elf, were both previously 0,
	increase gnome infra from 3 to 4
config.h, death.c, dungeon.c, externs.h, main.c, misc2.c, signals.c:
	modify wizard mode code, no longer need password or correct UID to
	enter, prints disclaimer then asks for confirmation
atarist.c, death.c, misc2.c, ms_misc.c, save.c, signals.c: eliminate all
	getuid() etc. code, only place these are still used is in main.c
	and unix.c
all: merge wizard/god mode, god mode nolonger exists
io.c, unix.c: problems with flush() on EOF, don't call check_input if EOF,
	also, check_input returns zero if got an error on the read, also
	inkey() clears msg_flag on EOF to prevent infinite loops exit_game/
	msg_print/inkey/exit_game/etc...
moria2.c: monster_death(), increase tries from 10 to 20 so that treasure will
	be created more often
constant.h: increase MAX_MALLOC from 101 to 125 to reduce compacting monsters
	messages, increase MAX_TALLOC from 150 to 175 because it is possible
	to get compacting objects message during level generation
constant.h: BTH_HIT deleted, no longer used


### 5.1.3 / 1990-3-28

moria2.c: facts(), multiply damage for missile by 2/3/4 instead of adding when
	use proper weapons, makes missiles much more useful
save.c: write store info and time into save file even if dead, so that can
	be restored on a resurrection
constant.h: change version to 5.1.3


### 5.1.3 / 1990-3-30

constant.h, externs.h, files.c, misc2.c, moria1.c, moria2.c, staffs.c,
wands.c, variable.c: modify how misc abilities vary with level, new variable
	class_level_adj, which holds the new adjustment values, bth, bthb,
	device, disarm and save affected
externs.h, misc2.c, moria2.c: critical_hits() has fourth paramter, that
	indicates whether to use CLA_BTH or CLA_BTHB class level adj
moria2.c: penalty for attacking invisible creatures modified, was minus
	lev * BTH_LEV_ADJ-1, now minus level * BTH_LEV_ADJ/2, hence warriors
	have less penalty than mages/priests
creature.c, externs.h, misc2.c, moria2.c: test_hit has a fifth paramter, that
	indicates whether to use CLA_BTH or CLA_BTHB (or even CLA_SAVE)
io.c: add abort() calls to move_cursor_relative(), print(), and put_buffer()
	to help find bugs in the code
save.c: eliminate support for versions 5.0.11 to 5.0.13
misc2.c: get_spell(), only say 'unknown spell' if it is actually an alphabetic
	character typed
treasure.c: delete one ring of adornment and one amulet of adornment,
	add arrows at level 15, and bolts at level 25


### 5.1.3 / 1990-3-31

store1.c: change missile cost to 5*pluses instead of 10*pluses, since missiles
	appear in groups of 20 or so, this is comparable to normal weapons
	which are 100*pluses
distribute 5.1.3 sources


### 5.1.3 / 1990-4-3

externs.h: ifdef out definition of errno for MSDOS
io.c: moved process.h include for MSDOS from middle of file to start
io.c: in print(), put_buffer(), and move_cursor_relative(), clear msg_flag
	before printing error message, prevents problems with '-more-' prompt
recall.c: roff_recall(), don't set normal movement bit near beginning,
	also delete code that prints 'never moves' if all move bits clear
spells.c: teleport monster will wake it up
scrolls.c: reading a second word of recall scroll does not reset timer
spells.c: wall building now heals Xorns in addition to earth elementals
spells.c: drain life damage increased from 50 to 75, to match other 50th level
	wand damages
constant.h, creature.c, moria2.c, variable.c: defined new class level adj
	CLA_MISC_HIT and replace all test_hit uses of CLA_SAVE with this,
	document that this is identical to save, and assumes all values same
treasure.c: two potions named poison, change first to weakness (lose str)


### 5.1.3 / 1990-4-4

save.c: add some __GNUC__ ifdefs so that the UNIX style code will be used,
	not the code for the broken atari st MWC libraries


### 5.1.3 / 1990-4-6

unix.c: two include files, sys/time.h and sys/resource.h are actually BSD
	specific, put #ifndef USG around them


### 5.1.3 / 1990-4-9

desc.c: remove all uses of "%+d", because AtariST/MWC and convex and probably
	others do not support it
spells.c: replace_spot(), clear lr flag, so that after destruction scroll,
	destroyed areas will not be lit by a light scroll in a room
creature.c: let monsters eat other monsters of equal exp, helps prevent
	using oozes as treasure factories
moria2.c: mon_take_hit(), if kill Balrog, always update recall info even if
	Balrog invisible, since will always know it was the Balrog (winner!)
recall.c: sleep values too hard to learn, change test from r_wake > sleep to
	(r_wake*r_wake)>sleep


### 5.1.3 / 1990-4-13

creature.c, moria2.c: mon_take_hit() was called within creatures() when
	monsters were trapped inside a wall, bracket call in creature.c with
	code to set hack_monptr, and add code to mon_take_hit() to check it
creature.c: add code at end of creatures() loop to fix2_delete_monster() in
	case monster died during movement (maybe it was in a wall?)
externs.h: add DGUX to USG ifdef for the 'extern int sprintf()' declaration
io.c: added code to save/restore local mode word of tty driver, i.e.
	TIOCLSET and TIOCLGET ioctl calls
save.c: for atarist && __GNUC__, call fopen with "rb" and "wb" for binary mode
io.c, unix.c: for atarist && __GNUC__, don't include <sys/wait.h>
externs.h: define sprintf as int (), if defined(atarist)


### 5.1.3 / 1990-4-14

externs.h, unix.c: three new functions tilde(), topen(), and tfopen()
	which perform ~ expansion for file names, in externs.h, define open to
	topen and fopen to tfopen for unix machines
config.h, dungeon.c, main.c, save.c, unix.c: add support for ANDREW
	distributed file system, SECURE define in config.h, disallows
	inferior shells, also instead of giving up setuid priviledges at
	start, calls ANDREW library functions bePlayer(), beGame(), and
	Authenticate()
unix.c: add ifdefs to use select() when compiling for xenix


### 5.1.3 / 1990-4-15

creature.c, externs.h, files.c, io.c, main.c, misc1.c, moria2.c, recall.c,
save.c, treasure.c:
	more Atari ST MWC fixes, eliminate fdopen(), make loc_symbol() return
	unsigned char, all (var & long_const) constructs assign long const to
	temp var first,


### 5.1.3 / 1990-4-16

Makefile: fixed problems with 'make install'


### 5.1.3 / 1990-4-19

atarist.c: add #include <stdio.h> at start, and #endif at end
misc2.c: tohit_adj() toac_adj() todis_adj() todam_adj() all used cur_stat
	instead of use_stat like they should have


### 5.1.4 / 1990-4-20

moria2.c: mon_take_hit code of 4/13 wrong, use monptr instead of i at end
misc2.c: remembering code wrong, add (i < 32) to stop it at end of spell list
moria1.c: when inven full and take off object, check for object under
	player before allowing/asking her to drop object
save.c: for atarist MWC port, ifdef out all of the open() calls, and only
	leave the fopen() stuff in
misc1.c: compact_monsters(), don't set delete_any if fix1_delete_monster()
	is called, because it does not decrement mfptr, call abort() if
	cur_dis goes below zero (i.e. can't delete any monsters)
all: run lint on 4.3BSD and SYS V
constant.h: change version number to 5.1.4 and distribute


### 5.1.4 / 1990-4-27

dungeon.c: add prt_map() at top after creatures(FALSE) call for ATARIST_MWC,
	fixes display problems for some reason


### 5.1.4 / 1990-5-1

moria1.c: inven_command(), clear msg line is ESC typed at "Drop all?" prompt


### 5.1.4 / 1990-5-3

all: eliminated upd_log(), and plog variable
doc/*: update all documentation, except moria.ms


### 5.1.4 / 1990-5-4

all: visit every subdirectory, create README or ERRORS files as needed,
	make everything as up to do as possible, split files greater than
	60K so that they can be posted to comp.sources.games easily
all: deleted all tmp.* files
externs.h, moria1.c, moria2.c, et al: moved functions out of moria1.c and
	moria2.c to reduce their size to less than 60K, many functions not
	moved were made static
dungeon.c, externs.h, main.c, misc2.c, save.c, variable.c: new variable
	to_be_wizard, allows resurrect without entering wizard mode,
	resurrect sets noscore = 0x1, wizard mode sets noscore = 0x2,
	prt_winner prints "Is/Was wizard" or "Resurrect" is noscore set


### 5.1.4 / 1990-5-5

misc1.c: moved see invisible from slay animal to slay undead weapons
doc/moria.ms: updated documentation to reflect changes from 4.873 to 5.1,
	added section which explains crowns, added some sections from
	Chris J Stuart's BRUCE Moria's documentation


### 5.1.5 / 1990-5-7

monsters.c: ogre mage is now evil
files.c: added extern int error for MSDOS
moria.ms: proof read it again
misc1.c: reduce plus to dam for slay dragon arrows from +10 to +3, no
	longer need such high damages because ptodam is multiplied now
	when used with right weapon
Makefile: test and fix 'make install'
ibmpc/ms_ansi.c: change two escape characters to \033
mac/moria.r: change many control characters to \xxx, update version info
	strings to 5.1.5
constant.h: change the version number to 5.1.5 and distribute


### 5.2.0 / 1990-5-9

constant.h, mac/moria.r: change the version number to 5.2.0
save.c: fix support code for 5.1.0 savefiles, because of minor version number
	change, update compatibility code for 5.1 and 5.2 savefiles
save.c: save/restore value of died_from string


### 5.2.0 / 1990-5-10

scrnmgr.r, scrntest.r: these files had control characters also, changed
	'...' to \311
resource.hqx: recompiled the mac resource files
macscore.c, mac.c: changed control characters '...' to \311
all: distribute version 5.2.0


### 5.2.0 / 1990-5-14

death.c, externs.h, io.c, ms_misc.c: Turbo C changes, void sleep() instead
	of unsigned sleep(), don't call reset_term() before exiting, ifdef
	out definition of sleep() in ms_misc.c, declare signal handler as
	void not int
generate.c, types.h: change IBMPC_TURBO_C defines to the proper TURBOC define
io.c: shell_out(), MSDOS code parameter to inkey() call deleted
externs.h: count_msg_print() changed from (int) to (char *)
io.c: get_check(), add code to use 'y' if LINT_ARGS defined, pause_exit()
	add code to use 'delay' for MSDOS machines
ms_misc.c: clear_screen(0,0) changed to clear_screen()
main.c: set stack size for TURBOC by declaring the _stksize variable
misc2.c: player_saves(), split expression up because MPW C couldn't handle it
save.c: include time.h for for the Mac, delete var 'i' in save_char (unused),
	get_char() add code for MAC to return FALSE if savefile doesn't exist
mac.c: add call to initsavedefaults() in main()
mac/ERRORS: document problem with mac Makefile


### 5.2.0 / 1990-5-15

externs.h: remove decl of py_bash(), which is in moria2.c and static


### 5.2.0 / 1990-5-17

dungeon.c: move search_off() call after check_view(), as panel_* variables
	must be valid before search_off() is called


### 5.2.0 / 1990-5-18

all: 7 files did not end with newlines, 5 hqx files, and origcmds.hlp,
	misc/README also did not end with a newline


### 5.2.1 / 1990-5-21

constant.h, version.hlp, moria.r: change version number to 5.2.1
ScrnMgr.doc: split all lines greater than 80 characters, to make the file
	more portable


### 5.2.1 / 1990-5-22

death.c: in mac sources, there was a 'true' instead of a 'TRUE'
ms_misc.c: Turbo C sscanf() incorrectly reads a newline from a blank line,
	add check for newline to code that reads configuration file
spells.c: door_creation(), called popt() before calling delete_object()
	which could then invalidate the value returned by popt, moved
	popt() call after the delete_object() call


### 5.2.1 / 1990-5-25

creature.c: fix the invincible/invisible monster bug, multiply_monster()
	was creating children on top of the parent, thus killing the parent
	for carnivorous monsters
death.c, externs.h, generate.c, io.c, main.c, ms_misc.c, signals.c, types.h:
	change all TURBOC defines to __TURBOC__
util/printit: new version of the printit program by Carl Hommel, fixed up
	to be somewhat more portable and to use only moria defined constants


### 5.2.1 / 1990-5-27

player.c, tables.c, variable.c: Turbo C can not accept any file with more than
	64K global variables, so variable.c split into three parts, also
	updated all makefiles to refer to all three files
mac files: all three mac Makefiles modified to delete obsolete references to
	CInterface.o and CRuntime.o, scrnmgr Makefile modified to automatically
	combine the two ScrnMgr.c parts, README updated to document a problem
	with the Makefile not appending resources and text files sometimes


### 5.2.1 / 1990-5-31

all: released umoria 5.2.1
ibmpc/ms_misc.c: corrected warn() function to use va_list, the old function
	was not strictly correct


### 5.2.1 / 1990-6-4

misc2.c: prt_stat_block() must also call prt_study()
misc2.c: random_object() could create objects outside of map boudaries,
	added an in_bounds() call to fix


### 5.2.1 / 1990-6-6

main.c: changed 'if (p = getenv(...))' to 'if ((p = getenv(...)) != NULL)'
	to avoid compiler warnings
moria1.c: rest_on(), add check to make sure rest_num fits in short
ibmpc/umoria.prj: new file, a project file for Turbo C 2.0


### 5.2.1 / 1990-6-9

io.c: HP-UX does not have VEOL2, added #ifdef VEOL2 around its use
dungeon.c: clear message line after change_character(), since as ESCAPE
	exit does not
moria1.c: for 'x' and 'w' commands, clear heavy_weapon flag before calling
	check_strength, so that always get heavy message when wielding heavy
	weapon, and never get light message when wielding light weapon
scrolls.c: identify scroll can move arbitrarily far after ident_spell(),
	replace simple 'moved one down' check with loop to search until found
main.c: fixed starup code so that don't get 'savefile doesn't exist'
	message anymore when starting a new game
all: apply fix to all uses of long constant with & | << operators to avoid
	a Mark Williams C bug on the Atari ST
externs.h, files.c: removed MSDOS declaration of errno from files.c, and
	remove ifndef MSDOS from errno decl in externs.h
misc1.c: changed name of variable 'clock' to 'clock_var' to avoid problem
	with a mac compiler (not sure which one)
io.c: every io.c file, remove '\0' as alias for ESCAPE from get_com() and
	get_comdir() (MAC only function)
moria1.c: light_dam() calls inven_damage(set_lightning_destroy, 3) which
	gives it a small chance of destroying objects in player's pack,
	this makes it just like the other *_dam() functions
spells.c: teleport_to(), add call to in_bound() to make sure player will
	be teleported to somewhere inside the dungeon


### 5.2.2 / 1990-6-10

config.h: add note about using GCC on an Atari ST
main.c, save.c, undef.c: eliminated the unused _new_log() function
death.c, dungeon.c, externs.h, save.c, undef.c, variable.c: eliminated the
	mostly unused log_index variable, the few uses of it were replaced
	with references to character_saved
externs.h, files.c, io.c, undef.c: init_scorefile() implemented, same as
	in umoria 4.87
main.c, undef.c: eliminate unused function init_files()
constant.h, moria.r, version.hlp: change version number to 5.2.2


### 5.2.2 / 1990-6-11

ibmpc/ms_misc.c: added space to GRAPHICS scanf, to try to eliminate problem
	with Turco C
death.c, externs.h, files.c, save.c, variable.c: extensive changes to
	implement the scorefile, total_points(), highscores(), display_
	scores(), set_fileptr(), rd_highscore(), and wr_highscore() defined
	scorefile holds 1000 entries max, has one sex/race/class entry per
	non-zero uid, has live characters also
unix/Makefile: fix the treatment of score file, don't copy over old one,
	just use touch to create one if it doesn't exist
undef.c: finally eliminated


### 5.2.2 / 1990-6-13

all: lint under 4.3 BSD and AIX 2.2.1 (SYS V)
scores: readd scores file, since it makes micro distributions much easier
death.c: modify savefile for single user machine, so that it tries to
	prevent a single character from appearing multiple times in the
	savefile, checks for identical names, and killed by = (saved)
player.c: eliminated the unused dsp_race[] array
death.c: added code to implement flock() for systems which don't have it,
	based on code written by CJS for BRUCE Moria


### 5.2.2 / 1990-6-17

all: fixes for VMS, define USG for VMS, merge in fixes from Todd Pierzina


### 5.2.2 / 1990-6-23

scrolls.c: for curse weapon, call py_bonuses(-1) before clear flags so
	that attributes will be turned off
moria1.c: calc_bonuses(), handle AC just like everything else, previously
	did not add in AC bonus if armor was cursed
creature.c, moria2.c: doors weren't being broken when bashed, change
	"randint(2) - 1" to "1 - randint(2)"


### 5.2.2 / 1990-7-14

all: merge in some changes indiciated by warnings/prototypes generated by
	MSC 5.x
recall.c: compute monster kill exp in long, to avoid overflow of int
all: added AMIGA support
amiga/: new directory to hold support files for the amiga
main.c: variable result was not initialized to false


### 5.2.2 / 1990-7-15

all: merge in more VMS patches
death.c, dungeon.c: fixed display_scores() and 'V' command problems
creature.c, dungeon.c, misc2.c, moria1.c: new feature '*' rest until
	reach max mana and hp, print error message for illegal rest count
all: removed improper define of NULL, new define CNIL for (char *)0, used
	instead of NULL where appropriate, eliminate strange include orders
	that the previous NULL define required
io.c: ifdefed out the include for termio.h, as this caused it to be included
	twice on some systems (curses.h includes it also) causing problems
macrsrc.h: changed MAX_RESTART from 37 to 36


### 5.2.2 / 1990-7-21

ms_misc.c: fixed reading of empty lines from config file for Turbo C


### 5.2.2 / 1990-7-22

io.c: fix Amiga code for flush()
types.h: reduce size of cave_type structure for Amiga/Aztec C 5.0
death.c: fix display_scores(), so that it only shows entries belonging to
	current player when show_player is true


### 5.2.2 / 1990-8-29

misc2.c: deleted some redundant code in gain_spells() where it calculates
	which spells a player can learn
spells.c: recharge(), could call randint() with 0 or negative numbers, check
	for these cases before calling randint()


### 5.2.2 / 1990-9-8

moria1.c: inven_command(), when drop/remove object and inven/equip are both
	empty, set inven weight to zero, just to be safe
dungeon.c: jamdoor(), when jam a door, decrement inven_weight by weight of
	one spike
moria1.c: inven_command(), make spikes wieldable objects, fight with them
	like missile weapons, i.e. they disappear after use
desc.c: objdes(), add damage multiplier of bows to their name
externs.h, io.c, misc1.c, save.c variable.c: new option sound_beep_flag, so
	that players can turn off bad character beep if desired
desc.c, misc2.c: modify known1_p() to return TRUE for objects which never
	have a color, modify objdes() to work with the new convention,
	modify inven_carry() so that objects are added to the inventory
	sorted by subval if they are known1_p()


### 5.2.2 / 1990-9-9

misc1.c: add_food(), fix so that does not use f_ptr->slow to set f_ptr->food
	value, this makes it much less likely to overflow the food level
misc2.c: inven_carry(), yesterday's insert in order fix does not work for
	items which can be unknown1p, only insert items ordered by subval if
	they are always known1p, i.e. object_offset() returns -1
externs.h, misc1.c, misc2.c, save.c variable.c: new option display rest/repeat
	counts, since these counts make resting at 1200 baud or less
	unbearably slow, people need to be able to turn them off
store2.c: get_haggle(), let RET default to the last inc/dec amount when
	haggling
misc1.c: compact_monsters(), return FALSE if could not delete any monsters
	instead of aborting, popm() return -1 if could not allocate monster
	(i.e. compact_monsters() failed), place_win_monster() abort if popm()
	fails which should never happen, place_monster()
externs.h: place_monster() now int type not void,
creature.c, misc1.c, spells.c: fix users of place_monster() to check result,
	and fail if place_monster() failed, really only necessary for the
	calls in creature.c, these fixes fix the monster list overflow bug
dungeon.c, externs.h, misc1.c: change compact_monsters() from static to
	extern, call it from within main loop in dungeon.c if there are less
	than 10 spaces left in the monster list, compact_monsters() is much
	more likely to be successful when called here than when called from
	creatures()


### 5.2.2 / 1990-9-11

signals.c: delete extra definition of error_sig for ATARIST_MWC
atari_st/README: update with GCC and Turbo C info
files.c: fix typo, filname1 -> filename1


### 5.2.2 / 1990-9-18

mac.c, ScrnMgr1.c: replace uses of KeyMapLM with GetKeys() call


### 5.2.2 / 1990-9-19

misc1.c: m_bonus(), could overflow on 16 bit machines when level >= 263
	when calculating stand_dev
death.c, externs.h, save.c: store the encryption byte for each savefile record
	immediately before it in the scorefile, makes the scorefile more
	robust in the face of a system crash, rd_highscore(), wr_highscore()
	no longer pass the encryption byte
death.c: added version numbers to the savefile
death.c, externs.h, save.c, variable.c: add max_score to savefile, which is
	the maximum score the character has reached so far, total_points()
	modified to use the larger of the computed score, and the value of
	max_score, this prevents a "(saved)" message on the score file for
	a dead character, which died having fewer points than when it was
	last saved


### 5.2.2 / 1990-9-26

death.c, externs.h, main.c, save.c, types.h, variable.c: new global variable
	birth_date, set to time when character created, saved in save files
	and scorefile, when loading character check to see if the a character
	in the scorefile has the same race/sex/class/uid/birth_date and
	its died_from is not "(saved)", in which case this character will not
	be scored
misc2.c: print "Duplicate" on screen for such duplicate characters
all: update all documentation, README, and other misc descriptive files
all: add fixes for Atari ST/Turbo C 2.0
all: lint on Sun3/uVAX/MIPS/RT_PC
recall.c: when printing out depth of monster, print Balrog as 50 not 100


### 5.2.2 / 1990-10-3

all: fix all problems reported by maniscan/cshar, except for control-L
	characters on a line by themselves


### 5.2.2 / 1990-10-7

creature.c: mon_move(), cast m_ptr->fy-1 to int, so that negative values
	will correctly compare to it, in the wall building code
spoilers: new file, lists spell damages
misc2.c: prt_experience(), always check whether exp is greater than max_exp,
	previously did not when player was at level 40
creature.c: mon_move(), set the recall CM_ATTACK_ONLY bit when a monster
	should have moved but did not, instead of the old method of setting it
	when a non-moving monster attacked
store2.c: get_haggle(), fix bugs in the automatic increment code, also, when
	reach final offer, set the automatic increment to the required diff,
	so that player need only hit return to bid the final amount
moria2.c: tunnel(), before checking for tunneling into a invisible monster,
	check to make sure player is tunneling somewhere which has an effect,
	otherwise, player can use this to 'detect' invisible monsters in
	open spaces by trying to tunnel there
moria1.c: calc_bonuses(), base AC should always be visible, as long as the
	item is not cursed


### 5.2.2 / 1990-10-8

spells.c: td_destroy2(), should not destroy chests, not disarms and unlocks
	them instead
misc2.c: put_misc3(), clear_from(14) not 13, was clearing the gold value
files.c: file_character(), add the "Max Exp" and "Exp to Adv" fields to
	the info written to a file, to match the on screen display
files.c: file_character(), set fd to -1, not NULL, code was closing stdin
	(== 0 == NULL) when could not open file
death.c: exit_game(), clear character_saved before calling highscores(),
	prevents inkey() from recursively calling exit_game() when eof on
	stdin has been detected
all: released 5.2.2 sources


### 5.2.2 / 1990-10-23

death.c: highscores(), must do fseek() after reaching EOF before changing
	read/write mode, previously only did this for VMS, now do this for
	all systems


### 5.2.2 / 1990-10-26

config.h, death.c, io.c: fix VMS errors, declare 'string' in death.c
	duplicate_character(), fix typos in other files
dungeon.c: for VMS, after calling kbhit(), must consume one character
vms/Makefile: fixed, mostly change constants.h to constant.h


### 5.2.2 / 1990-10-30

death.c, externs.h, files.c, save.c: define STDIO_LOADED after including
	stdio.h, then use it to control declarations in externs.h
constant.h, death.c: define SCOREFILE_SIZE as 1000, and use it in death.c
moria2.c: cast_spell(), when don't have enough mana, use 'gods may think you
	presumptuous' message instead of 'summon your limited strength'


### 5.2.2 / 1990-11-4

ibmpc/moria.cnf: add ^M, since otherwise MSDOS moria can't read file properly
io.c: include stdio.h explicitly, and define STDIO_LOADED, so savefile
	can be closed when shelling out
store2.c: get_haggle(), clear increment when invalid increment entered
store2.c, variables.c: last_store_inc contradictory declarations, changed both
	to int16
externs, misc2.c, sets.c, spells.c: set_*_destroy changed to take inven_type
	pointer instead of tval, modified so that (Rx) items won't be
	destroyed by attacks of type x
death.c: for single user systems, use birth_date instead of name when
	looking for matching characters in savefile
death.c: MSDOS setmode() needs file descriptor, not FILE pointer
files/*: removed all TABS, since some systems do not handle them properly,
	specifically, Turbo C on the Atari ST and IBM-PC
misc1.c: rings of searching cost was 100*p1, amulet of searching cost 20*p1,
	change both to 50*p1
mac/macio.c, ibmpc/tcio.c: modify beep() so that it uses the new option flag
desc.c: changed the (*2) string for bows to (x2)
spells.c: slow monster and confuse monster modified to wake up monster if
	the attacks fails
spells.c: wall_to_mud() and dispel_creature(), print messages even if
	monster not visible since the effects are visible/audible
moria2.c: for fire/acid/gas traps, print the trap message first, and then
	damage an item
treasure.c: change name of lose memories potion to lose experience, so it
	won't be confused with monster memories
spells.c: trap_creation(), don't create a trap under the player, to prevent
	strange occurances such as ending up under a falling rock
moria2.c: tunnel(), when wielding heavy weapon, make tunneling more difficult


### 5.2.2 / 1990-11-6

vms/getch.c: New code for kb_hit(), which was tested and works under VMS 5.2
	the old code did not work for VMS 5.2


### 5.2.2 / 1990-11-9

misc1.c: compact_monsters(), distance test was backwards!, now delete monsters
	farther away first,  allow a final pass with distance of 0,
	never delete the Balrog during compaction


### 5.2.2 / 1990-11-21

doc/history: rewrote history file to bring it up-to-date
misc2.c: gain_spells(), don't need to be able to read spell books if learning
	priestly spells, don't fail if blind or no light
death.c: fix #ifdef typo around <time.h> include


### 5.2.2 / 1990-12-1

store2.c: when good bargainer, change string to "final offer", and allow
	return to accept final offer
player.c: change rogue start inv from Soft Armor to Cloak, so that it is the
	same as the other non-Warrior classes
dungeon.c: decrement command count after do_command() instead of before,
	so that all counted commands will work right, this fixes 3^P


### 5.2.2 / 1990-12-5

death.c: display_scores(), fix VMS bug, truncate uid to a short before
	comparing against the uid in the score file record, also move
	the uid computation outside the loop to speedup the procedure
io.c: put_buffer (), for Atari ST, was printing the untruncated out_str
	instead of tmp_str


### 5.2.2 / 1990-12-14

atari_st/curscomp/curses.[ch], death.c, files.c, io.c, signals.c:
	add Atari ST TC fixes for the new (in 5.2.2) code


### 5.2.2 / 1991-1-4

spells.c: light_area(), always light area immediately next to player even if
	in a room, could be standing on the edge of a room
monsters.c: Grave Wight, no longer has confusion spell, no other wight/wraith
	has it
misc2.c: get_spell(), when enter invalid character, print "You don't know
	that prayer." instead of "spell" for priests/etc.
creature.c: make_attack(), creatures which are repelled should not be confused
	because they did not hit the player
death.c: exit_game(), delete #ifndef TURBOC around the restore_term() call
io.c: restore_term(), delete the call to clear() in the MSDOS code, it was
	trying to use curses after curses had been exited


### 5.2.2 / 1991-1-22

files.c: call pause_line(23) after printing hours file
constant.h, config.h: constant.h should always be included before config.h,
	because some systems redefine constants in config.h
rnd.c: include config.h after constant.h
main.c, misc2.c, save.c, signals.c: include constants.h before config.h
misc2.c, vms/getch.c: new function user_name() for VMS, fix get_name()
	in misc2.c to call it


### 5.2.2 / 1991-1-30

moria2.c: hit_trap(), add msg_print(CNIL) for the trap door case


### 5.2.2 / 1991-2-4

io.c: for ATARIST_MWC, use 240 instead of '#' for walls
save.c: for ATARIST_MWC, convert 240 to '#' when saving, and '#' to 240
	when loading, to avoid conversion problems


### 5.2.2 / 1991-2-8

create.c: monval(), cast i to int, otherwise some compilers do the arithmetic
	with unsigned characters


### 5.2.2 / 1991-2-19

makefile: add new macro CURSES, define it for BSD/SYS V/and Xenix
config.h: add config info for XENIX, define SYS_V and unix, only undefine
	register for MSC versions less than 600 (6.00?)
creature.c: mon_move, comment out register decl for r_ptr for XENIX systems
	to avoid a compiler bug
misc2.c: place_gold, comment out register decl for t_ptr for XENIX systems
	to avoid a compiler bug
unix.c: ifdef out include of termio.h, for XENIX add include of sys/types.h
	and define bzero as memset, test for unix or M_XENIX at the top
Makefile: add optional commands to install target which chown/chgrp everythin
	to bin, and put pointer to it at the top


### 5.2.2 / 1991-2-25

util/score: Two new utilities, prscore to print scorefiles, and delscore to
	delete one entry from a scorefile.
config.h: add MORIA_* macros for the Atari ST with GCC
death.c, externs.h, io.c, main.c, signals.c, variable.c: Apply Atari ST/GCC
	patches from Scott Kolodzieski.


### 5.2.2 / 1991-3-1

death.c: Amiga must open/close scorefile like MSDOS&VMS
io.c: init_curses(),restore_term(), fix bugs in AMIGA code,
	add code to release resources
amiga/*: updated versions of amiga source files, from
	<EMAIL>, Corey Gehman
atari_st/curscomp: complete rewrite of the curses code by Hildo Biersma
store2.c: get_haggle(), do not accept an increment value of zero, turn off
	increment flag instead of accepting it


### 5.2.2 / 1991-3-2

store2.c: store_purchase(), store_prt_gold call was inside `if' now after,
	did not update gold if store had 13 items and you bought the 13th


### 5.2.2 / 1991-3-11

moria1.c: sub3_move_light(), don't print over old location if find_flag
	is true, unless find_prself is also true, this speeds up movement
	in find mode by eliminating unnecessary drawing of characters
moria2.c: hit_trap(), call move_light() for the teleport trap case, to light
	up the trap
misc1.c, save.c, treasure.c: change ATARIST_MWC ifdefs for using graphic
	character to ATARI_ST which is true for both MWC and TC
io.c: remove all ATARIST_MWC diffs which were needed for the old non-standard
	curses, change the rest to be ATARI_ST, since both MWC and TC need them


### 5.2.2 / 1991-3-14

source/*: add Mac THINK C support
mac/dumpres/*: add Mac THINK C support
mac/scrnmgr/*: add Mac Think C support
moria1.c: find_init(), when !light_flag and !find_prself, must erase the
	player's '@', because sub3_move_light() won't, see 3/11 change above


### 5.2.2 / 1991-3-15

mac/*: add Mac THINK C support
*: put file name and 1991 copyrights in all source files


### 5.2.2 / 1991-3-23

save.c: prevent resurrection of a total winner character
constants.h, creature.c, monsters.c, recall.c: add new flag CM_ONLY_MAGIC,
	set this flag in creature.c, check the flag in recall.c, allows
	recall to print movement speed for Quylthulgs
creature.c: when a wand is drained of charges, inscribe it as {empty} if
	it is not known2


### 5.2.2 / 1991-3-24

files.c, ibmpc/ms_misc.c: ifdefed out msdos_intro(), since that routine is
	obsolete now
doc/moria.6: add -S option to list at the top
ibmpc/CONFIG.DOC: update for Umoria 5.x, remove kneller's address, and put in
	my address


### 5.3 / 1991-3-25

config.h, constant.h, */*.c: move VMS definition for ESCAPE from config.h
	to constant.h, now all files include config.h before constant.h
*: linted all sources files, changed version numbers to 5.3


### 5.3.1 / 1991-3-30

vms/*, ibmpc/ms_misc.c, config.h, death.c, dungeon.c, externs.h, files.c,
io.c, save.c: merge in changes from Ralph Waters, which are needed to compile
	the sources under VMS and IBM-PC/Turbo C.
moria2.c, store2.c, *.c: get_item(), show_inven() take new parameter mask,
	if mask is non-zero, they only list items indicated by mask array,
	store_sell() calculates a mask based on what store will buy
store2.c: purchase_haggle(), sell_haggle(), if the auto increment is larger
	than the difference between the store's offer and the player's offer,
	then set the auto increment to the exact difference
dungeon.c, externs.h, moria1.c, moria2.c, variable.c: eliminate search_flag,
	it was redundant, replace all uses with (py.flags.status & PY_SEARCH)
tables.c: remove good armor items from armory, to force players to search for
	them in the dungeons, hard leather boots, iron helm, partial plate,
	full plate
misc1.c: alloc_monster(), always create dragons sleeping here, to give the
	player a sporting chance
moria1.c: inven_command(), when pack not empty and show_weights flag true,
	display capacity along with weigth carried on first line of inventory
spells.c: build_wall(), permanently light walls created within range of
	player's lamp
spells.c: earthquake(), fix it to act just like build_wall when a monster is
	trapped in a wall
creature.c, externs.h: movement_rate(), now static
*: release 5.3.1 sources


### 5.3.1 / 1991-4-27

ms_misc.c, externs.h: change declarations of warn() to match definition,
	change declaration and definition of error() to match warn(),
externs.h: fix declarations for sleep(), find_init(), show_inven(), get_item()
death.c: display_scores(), don't set player_uid for non UNIX/VMS system
	duplicate_character(), ifdef out code which is unreachable for non
	UNIX/VMS system, make all returns have a value
sets.c: set_null(), add a #pragma argused for TURBO C
ms_misc.c: fix three lines that had an assignment inside an if
externs.h: add prototypes/declarations for VMS files getch.c and uexit.c
moria1.c: see_wall(), change ATARIST_MWC ifdef to ATARI_ST
atari_st/curscomp/curses.c: winsch(), first loop ran in wrong direction
externs.h: add declarations for atari st functions
atari_st/moria.prj: new file, TC project file for Umoria
death.c: highscores (), change fseed to fseek, typing error
creature.c, death.c, desc.c, dungeon.c, files.c, io.c, moria1.c, moria2.c,
	store2.c, wizard.c, atarist.c: include stdlib.h if ATARIST_TC to get
	prototypes for standard library functions
generate.c: for ATARIST_TC, include <string.h>
atarist/curscomp/curses.h: change mvadd* macros from compound statements to
	conditional expressions, so that all returns values are error checked
io.c: for ATARIST_TC, include ext.h to properly define (?) sleep
config.h: for ATARIST_TC, define index strchr
save.c: sv_write()/get_char(), define t_ptr for both MSDOS and ATARI_ST;
	get_char(), change ATARIST_MWC ifdef around chmod call to ATARI_ST
	include time.h for ATARIST_TC
unix/Makefile: change ONWER to OWNER
creature.c: creatures(), give moves to monsters trapped in rock, so that they
	will die/dig out immediately, mon_move() if a monster in rock is
	already dead, don't kill it again
*: update address info in all files
io.c: change __GNU_C_ to __GNUC__
config.h: the test for undefining 'register' was wrong, it was undefing it
	for all non-MSC compilers
moria2.c: tunnel(), heavy weapon code wrong, eliminate >>2 of penalty, add
	penalty instead of subtracting it
help.c: ident_char(), add period after Giant Frog.
monsters.c: novice priest, change sleep from 10 to 5 to match other novice 'p'
moria1.c, store2.c, *.c: get_item() new parameter 'message', when invalid
	letter hit, print this message if non-zero instead of beeping,
	store_sell() pass message "I do not buy such items.", fix all other
	callers to pass CNIL


### 5.3.1 / 1991-4-28

misc2.c, files.c: put_misc2(), file_character(), when player at max level,
	don't print a number for Exp to Adv, instead print ******
io.c: msg_print(), put multiple messages on the same line if they are short
	enough


### 5.3.1 / 1991-5-22

externs.h: ifdef out declaration of sprintf for NeXT
io.c (init_curses): correct atarist/GNUC code for signal call, ifdef was wrong


### 5.3.1 / 1991-7-6

spells.c (unlight_area): Unlight all floor spaces with `lr' set, instead of
	just the room floors spaces.  This darkens the doorways.
moria1.c (light_room): Add code to set the fm flag, necessary so that the
	above fix does not unlight doors that it shouldn't.
io.c (msg_print): Don't combine NULL messages with other messages.
save.c (get_char): Use msg_print when printing the `departed spirit' message.


### 5.3.1 / 1991-7-26

store2.c (purchase_haggle, sell_haggle): If the automatic increment plus the
	last offer passes the shop keepers current ask, then clear the incr.


### 5.3.1 / 1991-10-5

*: Add changes needed to prevent warnings from the IBM-PC TURBO C compiler.
misc[1234].c, moria[1234].c: misc[12].c and moria[12].c were each split into
	two files, because they were too large for TURBO C's integrated
	environment
*: adjust all makefiles, externs.h, etc to account for new moria/misc files
TCCONFIG.TCU, TCPICK.TCU: new files, uuencoded copies of Turbo C setup files
config.h, ms_misc.c: New define USING_TCIO, used to prevent including curses.h
	in ms_misc.c.  Defaults to defined if using TURBOC on an IBM-PC.
io.c: delete special purpose AMIGA code, it now uses curses
amiga/amiga.h: Deleted.
amiga/amiga.c: Delete all curses stubs.


### 5.3.1 / 1991-10-6

macrsrc.h: change type of resType, ResID to long
macrsrc.c: eliminated search_flag from macrsrc.c (see 3/30 changes)
config.h: put back switches RSRC, RSRC_PART1 and RSRC_PART2
ScrnMgr.ro: changed def of MBAR #228 (fixes crash on Mac Plus) and INFO #1
	(Make default window be full screen)
ScrnMgr.c: check for reconfig flag enabled for THINK_C, add recognition
	of MacClassic (and LC?) keyboard, now assumes unknown keyboard type
	has control key, other misc cleanups
moria.ro: changes version string
macconf.c: config.h included for consistency
mac.c: added support for 8-column tabs
mac/Install.doc: new file, installation instructions for THINK C
macconf.c, machelp.c, macscore.c scrnmgr.c: covered up error in THINK C
	includes files OK/Cancel for ok/cancel
death.c, save.c: delete setmode calls for IBM-PC, instead open files in binary
	mode


### 5.4 / 1991-10-12

*: Changed version number to 5.4.
save.c: change code to accept savefiles with version numbers greater than the
	version number of the game, savefile format frozen as of 5.2.2
externs.h: ifdef out the troublesome sprintf declaration
config.h: force `unix' to be defined for unix systems, since some stupid
	systems (e.g. AIX) don't already define it


### 5.4 / 1991-10-15

externs.h, moria4.c, ms_misc.c: correct typos discovered under MSDOS


### 5.4 / 1991-10-19

spells.doc, exp.doc: New documentation files.


### 5.4 / 1991-10-26

vms/uexit.c, externs.h, io.c, signals.c: Define uexit as void, and ifdef out
	exit declarations when VMS.
vms/moria.opt: add misc[34].obj and moria[34].obj
ibmpc/ms_misc.c: correct typo in error()
pr_items.c, pr_monst.c: main now returns 0
CONFIG.DOC, TERMCAP, ms_ansi.c: use le/do instead of obsolete bc/xd
dragon.inf: moved from util/weapons to doc, and updated it to be accurate
spoilers: Update from USENET FAQ posting.


### 5.4 / 1991-11-17

io.c: ifdef out code checking for 8 char TABS, because it assumes that the
	screen is exactly 80 characters wide
moria[12].[ms/txt]: Correct a few typos.
*: Fix all file permissions.


### 5.4 / 1992-7-16

Maintenance taken over by David Grabiner

moria4.c: bash(), use random direction if player is confused
spells.c: fire_ball(), fire_bolt(), don't update recall if monster not lit;
	this can happen if bolt hits an invisible monster
spells.c: speed_monsters(), sleep_monsters2(), dispel_creature(), 
	turn_undead(), only affect creatures within MAX_SIGHT 
spells.c: mass_poly(), area of effect should be <= MAX_SIGHT, was <
spells.c: destroy_area(), remove light from player's spot
spells.c: enchant(), add new variable limit, chance of failure is now
	(plusses/limit), with very slight chance of success over limit
scrolls.c: when enchanting melee weapons to damage, set limit to weapon's
	maximum damage, otherwise use 10 to give behavior similar to old method
misc2.c: magic_treasure(), make standard deviation of damage bonus on a melee 
	weapon proportional to weapon's maximum damage; these changes mean
	that daggers can no longer become powerful weapons
treasure.c: the Zweihander has now become a great weapon, value increased from
	1000 to 1500
externs.h: fix declaration for enchant()
staffs.c, wands.c: give everyone a slight chance to use difficult wands and
	staffs, otherwise a warrior will never be able to use many items


### 5.4 / 1992-7-23

death.c: print_tomb(), insert msg_print(CNIL) so that "You are using:" and
	"You are carrying:" don't get combined as one message; this made it
	impossible to see the equipment list
store2.c: haggle_insults(), insert msg_print(CNIL) so that insult is always
	recognizable
store2.c: purchase_haggle() and sell_haggle(), new variable didnt_haggle,
	don't call updatebargain if no haggle
store1.c: noneedtobargain(), changed to sliding scale, (good-3*bad) must
	be more than 5 + (price/50) to skip haggling, so that haggling for
	cheap items is over quickly, but can still eventually skip
	haggle for all items
store1.c: updatebargain(), now update for all items worth >9 gold, instead
	of just 10-999, since it is now possible to skip haggling for more
	valuable items as well


### 5.4 / 1992-7-25

moria4.c: bash(), unsuccessful bash takes a turn; otherwise, you can
	attempt to bash in different directions while confused or to locate
	invisible monsters; eliminate variable no_bash


### 5.4 / 1992-7-27

check all above changes
moria4.c: bash(), get "You bash at empty space" method when bashing a
	wall, corrected to "nothing interesting	happens"; this also
	prevents bashing from locating a secret door


### 5.4.1 / 1992-8-9

merge in all changes from 5.4.0 to 5.4.1
creature.c: update attack information only if monster is visible; update
	death information even if monster is not visible, since
	information will be on tombstone
*: change version number to 5.5.0


### 5.4.1 / 1992-8-12

spells.c: enchant(), guard against randint(0) if called with limit of 0
	(it shouldn't be).
moria4.c: throw_object(), py_bash(), don't do negative damage
shortnam.sed, spells.c: fire_ball(), fix spelling of "envelops"
doc/faq: remove old spoilers file, and put current FAQ here instead
*: put my name (DJG) in credits as contact
*: change copyright date in all source files to 1992


### 5.5.0 / 1992-8-13

release umoria 5.5.0


### 5.5.0 / 1992-10-26

doc/moria[12].[ms,txt]: correct some typos, and make changes for 5.5.0


### 5.5.0 / 1992-10-31

misc4.c: scribe_object() allowed inscriptions longer than 12 characters
	if 13-24 characters availble for inscription, could overwrite
	other data


### 5.5.0 / 1994-6-6

scrolls.c: aggravate monster should give "humming noise" before "stirring"
scrolls.c: always identify scrolls which print a message
unix/unix.c: change from obsolete getpw() to getpwuid() to get UID
death.c: #include<sys/types.h> seems to be needed on XENIX and SYSV
death.c: fix #ifdef (...) || defined(...)
save.c: set fd=-1 after closing file to prevent double close
dungeon.c: move hero/superhero to first status check so that player's HP won't
	go below 0 in mid-turn (killing him) and then become positive
doc/moria[12].ms: fixes so that file works with groff
store1.c: sell_price(), check for consistent sale prices compared
	pointers rather than values
create.c: get_all_stats(), set player level to 1 before calling set_use_stat
	with constitution (which used level to check hit points)
misc3.c: misspelled variable "holderr" in MWC-specific code
misc3.c: prt_experience(), put check against max level in while loop
	so that level gain is never tested if player is max level
misc3.c: gain_level(), corrected comment for loss of extra experience
	when player gains multiple levels
moria3.c: monster_death(), don't make player a winner if already dead
store2.c: get_haggle(), %d should be %ld
misc3.c: todis_adj(), case of dexterity 3 was omitted
spells.c: wall_to_mud(), may find objects in rubble as with tunneling


### 5.5.0 / 1994-6-7

io.c, signals.c: included changes from Andrew Chernov for 386BSD support
io.c, config.h: included changes from Berthold Gunreben for HP-UX support
config.h, death.c, files.c: added patches for HP Apollo, which doesn't allow
	multiple processes to use the same file
config.h: defined MORIA_LIB to contain pathname for moria/files, to 
	simplify configuration
moria1.c: inven_command(), get_item(), added code from Harry Johnston 
	which allows use of 0-9 to specify an item with the 
	corresponding inscription
doc/moria[12].ms: documented above change
files/version.hlp: my name appeared both as author and "other contributor"
scrolls.c: set AC bonus on cursed weapon, hit/dam bonuses on cursed armor
	to zero (in case HA/DF/gauntlets of slaying had bonus)
creature.c: don't print message when invisible monsters recover from bash
creature.c, moria3.c, spells.c: reworked monster confusion, monster's
	confused value now gives duration, turn_undead has guaranteed
	duration equal to player's level, other confusion random
creature.c: undead which have been turned will flee rather than moving
	randomly, attack only if cornered
recall.c: print "It is resistant to" if monster has not cast any spells 
        but breath flag is known (because monster resisted an attack)
monsters.c: allow monsters to resist attacks if they have no breath
	weapon but use the attack type (so fire giants resist fire)
sets.c: new function set_large(), identifies objects too large to fit in
	a chest or be carried by small creatures
misc3.c: get_obj_num(), new parameter must_be_small to generate only
	small objects when appropriate; place_object() passes it
files.c: random object sample passes must_be_small
constant.h, treasure.c, monsters.c, moria3.c, recall.c: new constant 
	CM_SMALL_OBJ for chests, and for monsters carrying only small
	objects, check it in monster_death() by setting a bit in
	treasure type, allow it to be recalled
moria3.c: summon_object(), object must be small if bit flagged above
many: change all other calls to place_object to set must_be_small to FALSE
externs.h: fix declaration of get_obj_num(), place_object(), add set_large()
store1.c: noneedtobargain(), change scale again, (good-3*bad-5) must be
	positive and its square must be at least price/50, this allows
	high-level characters to become good bargainers for expensive
	items


### 5.5.0 / 1994-6-8

lint all above changes, fix assorted typos
recall.c: recalled spell frequency for non-spellcasters in wizard mode
monsters.c: checked small objects/resistances for consistency, fixed error
creature.c: creatures given resistance by setting of breath bits tried
	to cast spell, calling randint(0)
moria3.c: error in testing type caused all monsters which should drop
	both objects and gold to drop only gold
creature.c: turned undead must call get_moves so they know which way to flee


### 5.5.0 / 1994-6-9

moria1.c: inven_command(), get_item(), print 0-9 in prompt message when
	appropriate 
moria[12].ms: clarified that digit inscriptions work only on items in pack
prayer.c: strengthened Holy Word
player.c: reduced failure chance for Holy Word 
check all changes, fixed more typos


### 5.5.0 / 1994-6-10

moria1.c: inven_command(), get_item(), 0-9 was printed in wrong place


### 5.5.0 / 1994-6-22

spells.c: td_destroy(), td_destroy2(), don't disarm/unlock chests that
	are already empty
treasure.c: up staircase had extra space after name
doc/moria[12].ms: proofread, fix many typos


### 5.5.1 / 1994-6-25

monsters.c: allow thieves to pick up objects on the floor
main.c,config.h,amiga/amiga.c,amiga/timer.c: included changes from
	Ronald Cook for Lattice C support on Amiga 
death.c,io.c,signals.c,unix.c,config.h: included changes from Brian
	Johnson for Linux support
changed version number to 5.5.1
fix more lint errors
util/mergemem: code from Eric Bazin to merge monster memories
*: changed all copyright dates to 1994
released version 5.5.1


### 5.5.1 / 1994-7-5

death.c: || !defined(APOLLO) should be &&


### 5.5.1 / 1994-7-11

store2.c: get_haggle, changed %ld back to %d since variable is 16 bits


### 5.5.1 / 1994-7-20

treasure.c: fixed many inconsistencies, mostly prices and names
misc2.c: magic_treasure(), fixed values of SU and SA, which weren't
	changed when see invisible was moved from SA to SU; also changed
	magical bonuses for these weapons
store2.c: increase_insults(), don't clear out haggling record if player
	is thrown out (it might be worse than zero), just penalize as
	for bad bargain 


### 5.5.2 / 1994-7-21

treasure.c: fixed a few more inconsistencies with items
files.c, misc32.c: file_character(), put_misc2(), don't print "Exp to
	Adv" if over max_level (i.e., winner) 
files.c: file_character(), leave enough space for printing 8-digit
	experience points 
misc3.c: put_misc2(), make display consistent with above change
misc3.c: new function prt_8lnum(), print a long variable in 8 digits of
	space, needed by revised put_misc2() above
death.c: need flock hack for HPUX
io.c: #include <termio.h> for HPUX was inside #if 0
ibmpc/*: fix several typos in PC-specific files, also one in config.h
changed version to 5.5.2
released version 5.5.2


### 5.5.2-1 / 2000-05-28 unstable; urgency=low

  * Initial Release.
  * Added DEBIAN_LINUX #define to handle code customizations.
  * Needed to include termios.h in io.c
  * All instances of signal() changed to sysv_signal() since libc6 defaults
    to BSD semantics.
  * Instead of redefining getuid, just ifdeffed it out of unix.c
  * Changed LOCK_EX and LOCK_SH definitions in death.c (to avoid
    redefinition warnings).
  * Library files are in /usr/lib/games/moria except for the scores file
    which is in /var/lib/games/moria and the hours file which has been
    renamed to /etc/moria-hours
  * Makefile changed to make the binary setgid instead of setuid, as
    required by Debian Policy 5.10.  None of the code itself needed to be
    touched, since it already relinquished uid and gid in the original code.
  * Saved game file is named ".moria-save" not "moria-save"

 -- Rene Weber <<EMAIL>>  Sun, 28 May 2000 16:35:38 -0400


### 5.5.2-2 / 2000-10-14 unstable; urgency=low

  * Fixed typos in the control/README.debian file
  * Updated the author's e-mail address in the copyright file
  * Updated the author's web page listed in the README.debian
  * Installed a new version of the FAQ (reflecting the author's move)

 -- Rene Weber <<EMAIL>>  Sat, 14 Oct 2000 10:23:48 -0700


### 5.5.2-3 / 2001-01-06 unstable; urgency=low

  * Added a Build-Depends line.
  * Changed the handling of the build directory for the convenience of
    porters (should have no impact on users).
  * Changed short description so that it does not include the name of the
    package.
  * Corrected path to /usr/games/moria binary in the menu.  Closes: #81353

 -- Rene Weber <<EMAIL>>  Sat,  6 Jan 2001 02:47:02 -0700


### 5.5.2-4 / 2001-07-09 unstable; urgency=low

  * Fixed dependencies (was compiled against libncurses4, what was I doing
    that day?).
  * Corrected explanation of the 'V' command in moria2.txt (but not in the
    nroff source for that documentation).
  * Removed use of dh_suidregister.

 -- Rene Weber <<EMAIL>>  Mon,  9 Jul 2001 23:31:01 -0400


### 5.5.2-5 / 2001-10-24 unstable; urgency=low

  * Changed location of score file from /var/lib/games/moria/scores to
    /var/games/moria/scores per FHS.  (Closes: #115849)
  * Fixed handling of score file on upgrades (do not null it out!).
  * Applied fixes to a few buffer overflows.  (Thanks to Lars Helgeland for
    noticing the overflows and supplying the patches.)  (Closes: #115745)
  * Upgraded policy to version *******.

 -- Rene Weber <<EMAIL>>  Wed, 24 Oct 2001 22:44:05 -0400


### <AUTHOR> <EMAIL>

	* types.h: use stdint.h to get guaranteed 16-bit and 32-bit types
	rather than relying on the length of a long, which varies by
	compiler.  

	* config.h: use ## operator so that MORIA_LIB works
	correctly.  

	* INSTALL: Note that default install is for Debian, so
	UNIX install needs to copy the Makefile.


### <AUTHOR> <EMAIL>

	* config.h: Gave up on ## because it is so ugly in ANSI C.


### <AUTHOR> <EMAIL>

	* config.h, externs.h, *.c: Include stdio.h and stdlib.h
	unconditionally rather than guarding them and sometimes dealing
	with incorrect prototypes
	* io.c: Include unistd.h for execl() on Unix and linux systems.


### <AUTHOR> <EMAIL>

	* config.h: Add new LICENSE file for GPL.
	* dungeon.c (do_command,original_commands), origcmds.hlp,
	roglgmds.hlp: New command ^V to view the GPL.
	* files/news: Added reference to GPL.


### <AUTHOR> <EMAIL>

	* All: Update all copyright/license notices to GPL; Files in
	atari_st/curscomp and mac/scrnmgr still refer to old licenses as
	they are stand-alone programs.
