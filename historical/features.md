# Major Features introduced between v4.87 and 5.x


You get a warning for spells or prayers beyond your strength.

Inventory commands (wear, wield, exchange, take off, drop) now always
take one move each. If anything significant happens during that move,
such as a monster moving somewhere, the screen will be redisplayed.
Inventories are displayed in the top-right corner of the screen, to minimize
the number of characters that must be redrawn afterwards, this makes the game
much more playable on slow speed modems.

When selecting items or spells, you can select with a capital letter to
print a description and confirm you really want that one.

You can now associate counts with commands. The `?` command shows you
the possibilities. In rogue command mode, simply type in the number.
In this mode, numbers are no longer simple commands.
In original moria command mode, enter a `#` to start a count.

There are options (set with the = command) to display weights in
an inventory, to highlight mineral seams, to ignore open doors
while running, etc.

Monster memory: use the / command to access info about a monster after you
have seen/killed at least one of them.  This can also be accessed from
the look `l` command.

Can enter control characters with two keystrokes using `^` key, useful for
noisy phone lines? or stupid terminals?

`M`ap command shows reduced size map.  `L` allows you to scroll around entire
map.  The `L` command also will now center the character on the screen.

Look `l` views all objects in an arc in the specified direction, or specify
`5` and it will look in every direction.

Can change your speed in wizard mode.

New field in status line, used for rest/paralysed/searching/repeat counts.

New field in status line shows speed.

Can die of starvation.

If carrying too much weight, you will be slowed immediately, the amount slowed
depends on how much over the weight limit you are.

Can not circle monsters (moving at the same speed as yourself)
by moving around them in a diamond pattern.

High level monsters have a chance of ignoring your bash, and they also have
a chance of recovering immediately even if stunned.  You can not bash the
Balrog.

Can abort rest/repeated command by hitting any character.

Every duration affect prints a command when it wears off previously
resist heat and resist cold, and perhaps others did not.

New find/run algorithm which is much better than the old one.  Also,
better handling of dangerous conditions that occur during repeat commands/runs.
An attack will always stop a repeated command and find/run mode.

Can inscribe names onto objects.  Some inscriptions are automatic:
an empty wand/staff is inscribed 'empty'.  An object which is used, but its
use does not identify it is inscribed 'tried'.  Cursed objects are inscribed
'damned' when identified as cursed.  If you detect an object is enchanted,
it is inscribed 'magik'.

Scrolls have their titles removed after being identified.  Scrolls/potions
from the store/dungeon can stack after the dungeon item is identified.

If you curse/damage a special object, such as a Holy Avenger sword, it
will lose its special property.

Will automatically save the game correctly if a HANGUP signal is received,
i.e. dropped dialup connection, the network crashes, etc.  The save file
is not a panic save, instead the program plays the game for you (i.e.
pretends that you are hitting ESCAPE) until it is safe to save the game.

Better handling of inventory/equipment lists, i.e. you can drop items
in the equipment list, can identify items in the equipment list, etc.

More consistent monster definitions, i.e. creatures with frost attacks
always take extra damage from fire (except those that also have fire
attacks), and many other fixes.

One source for the UNIX/IBM/Atari ST/Amiga/Mac/VMS versions, should make
keeping the micro versions up-to-date much easier.  The game is smaller and
hopefully faster.  Savefiles are now relatively machine independent, they
should transport from one mini/micro to another without any problems.

If you are hallucinating, `^R` won't help.

Bows can have a plus to damage.  These pluses only apply if you are
using the bow to fire the appropriate type of missile (arrow/bolt/etc).

Can haggle incrementally in the stores, i.e. can increase bid by 10
by typing +10, similarly -10 will decrease bid by 10.

Beeps if invalid character entered at most prompts.

Can type `^R` at any time to refresh the screen.

Armor/weapons show their base AC/hit/dam values in their name.

Any of `RETURN (^M)`, `LF (^J)`, `SPACE`, or `ESCAPE` can be typed at `-more-` prompts.

When dropping a group of items, you have the choice of dropping only one,
or all of them.

There is a small chance that a player will notice that there are unidentified
magic items in the inventory.  The chance is larger if the item is in the
equipment list.

Object distribution has been changed so that when you are deep in the dungeon,
there are many more high level objects created than low level objects.

Monster distribution changed so that when you are deep in the dungeon,
you will meet many more high level monsters than low level monsters.

Additional spikes make a door harder to open, in a progressively decreasing
sequence, that is, the more spikes you add, the less effect each additional
spike has.

When a stat changes, all abilities/etc that depend on the stat will immediately
change.   For example, if you CON increases, then your hit points will increase,
if your INT/WIS increases then your mana will immediately increase and you
will immediately learn new spells/prayers.  This makes the Grape Jelly trick
completely unnecessary, and, in fact, it will has no effect.

Spell/prayer letters never change, i.e. if you know the second spell of a book,
it will not be referred to as spell `a` if you do not know the first spell.

When haggling, if you have reach the final offer much more often than you
haven't, then the store will give you the benefit of the doubt and go
directly to the final price.

When wielding a weapon that is too heavy, the to hit penalty will be shown
by the `C` command.

It is harder to hit monsters when you can't see them.

Perfect stealth is impossible now.

Save files are now encrypted.

Slay monster items are now slay animal.  The previous slay monster item
did not work as stated by the documentation, fixing it to work reasonably
would have made it too powerful.

If picking up an item will slow you down, you are asked if you really want
to pick it up.

Spells work differently now.  You no longer learn them automatically when
you gain a level.  Instead, `Study` appears in the status line when are
capable of learning new spells.  Use the `G` command to actually learn
the spells.  The `G` command can be used anytime that you are eligible to
learn new spells, as for example, after quaffing a restore intelligence/wisdom
potion.

The object naming routines are completely rewritten.  This makes the program
much smaller.  Also, too long names will no longer cause a crash or error,
they will be silently truncated as necessary.

You can not attack an invisible/unlit monster in a wall by merely moving
into the wall, instead you must tunnel into the wall.  Since moving into
a wall is a free turn, people could take advantage of this while fighting
invis monsters by checking all of the walls first.

To change the default moria save file name, define the environment variable
"MORIA_SAV" to be the full pathname for where you want your save files.

"moria -n" will start a new game, and will ignore "moria.save" if it exists

Jellies and molds never carry treasure.

Recharging a high level wand, or a wand which already has many charges, is
much more likely to fail.

Elves and half-elves have infravision now.

There is no longer a distinction between wizard and god mode; only wizard mode
exists now.  Also, wizard mode no longer requires a password, and anyone
(not just the installer) can enter wizard mode.  Hence, wizard now works the
same no matter whether you are playing on a micro or a mini.  Enter wizard
mode by using the `^W` command, help is `^H`. (Or for the rogue-like option,
`^W` and `\`).

Missile weapons are much more powerful when used with the proper weapon.
For example, firing a bolt from a heavy crossbow multiplies its damage by 4.
rounded pebble, iron shot: sling = 2x
arrow: short bow = 2x, long bow = 3x, composite bow = 4x
bolt: light crossbow = 3x, heavy crossbow = 4x
Arrows and bolts appear more often deep in the dungeon.
Magical missile weapon prices are more reasonable.

The miscellaneous abilities `fighting`, `bows/throw`, `saving throw`, `disarm`,
and `magical device` are all level dependent, i.e. they improve as you gain
levels.  Formerly, the rate of improvement was independent of class, now,
however, the learning rate is clas dependent.  For example, warriors gain
proficiency at fighting twice as fast as mages/priests now.  As a result,
high level warriors are better fighters than before, and high level
mages/priests do not fight as well as they used to.  The complete table is:

                Magic       Saving
             Fighting  Bows  Devices  Disarm  Throw
    Warrior     4        4      2        2      3
    Mage        2        2      4        3      3
    Priest      2        2      4        3      3
    Rogue       3        4      3        4      3
    Ranger      3        4      3        3      3
    Paladin     3        3      3        2      3

In all cases, three is equivalent to the old rate.

umoria now understands tilde (`~`) characters at the start of a file name.

To resurrect a dead character, use `moria -w filename`.

New status line shows "Is wizard" if in wizard mode, "Was wizard" if
previously was in wizard mode, "Resurrected" if character was resurrected,
and "Duplicate" if character is already on the scoreboard.

Moved see invisible from slay animal to slay undead, since this makes a lot
more sense.

Dragon lightning breath attacks can now damage items in your inventory,
just like all other dragon breath attacks.

New scoreboard code added.  It will hold 1000 scores by default, scores for
saved characters are listed, can see all scores or just your scores.
On multiuser OS's, each user can only have one entry in the scoreboard for
each legal race/sex/class combination.  The score file format is portable,
and should be able to move from one machine to another with no problems.

The `-s` option will show all scores in the scoreboard.  The `-S` option
will show only your scores.  The `V` command will show all scores in the
scoreboard.  A second `V` command typed immediately afterwards will show
only your scores.

Typing `*` for the rest count will make you rest until you reach max mana
and max hp.

Spikes can now be wielded as a weapon for completeness.  Note however that
they make terrible weapons.

The damage multiplier for bows is now displayed as part of their name.

Two new `=` command options: can turn off sound, and can turn off the rest/
repeat counts displayed on the bottom line.  The last is useful for those
playing at 2400 baud or less, or on very slow machines.

Many items are now added to the inventory in a sorted position, e.g.
the spell books will always end up in the inventory sorted by their level.
This automatic sorting does not work for objects which have a 'color',
e.g. potions, wands, staffs, scrolls.

Typing RETURN while haggling will default to the last increment/decrement
amount used.

Umoria 5.4:

Prints multiple sentences on the message line if they are short enough.

Umoria 5.5:

The maximum damage enchantment for a weapon is now equal to its maximum
damage, instead of being fixed at 10 (there is a slight chance of
enchantment beyond this level); thus daggers are no longer the best
weapons for mid-level characters.

Umoria 5.5.1:

If you inscribe an item with a single digit (using "{" to inscribe), you
can refer to that item by the digit when it is in your inventory.  You
can thus label your pick as item 1, and wield item 1 whenever you need
the pick without checking which letter the pick is.

The prayers Turn Undead and Holy Word have been strengthened.

Creatures with fire attacks now resist your fire attacks, and you will
learn this in the recall information.

_last modified 6/25/94_
