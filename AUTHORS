# Moria / Umoria Authors

<PERSON> created Mo<PERSON> from 1981-86. <PERSON> took
the VMS Moria sources and created Um<PERSON> from 1987-94.

In 2008 Moria/Umoria was re-licensed under the GNU General Public License
version 3.0-or-later. All new additions to the code will fall under this license.


## Umoria 5.7.x

<PERSON> [-MRC-] (GPL-3.0-or-later)
  Umoria restoration project:
    Windows and macOS support
    deprecation of old computer systems (Amiga, Atari ST, etc.)
  major refactoring of the source code


## VMS Moria 0.1 - 4.8 / Umoria 4.8 - 5.6

Many people have contributed to the game since Koeneke released Moria
in 1983. Their names, license details, and contributions summary are
presented below.


<PERSON> [-RAK-] (GPL v2)
  Moria creator

<PERSON> [-JEW-] (GPL v2)
  <PERSON> creator

<PERSON> [-DJG-] (GPL v2)
  handle large items in chests
  as maintainer

<PERSON><PERSON><PERSON>. [-JWT-]
  character generation (race, class, stats, etc.)
  character adjustments (AC, HP, INT, WIS, damage, to hit)
  calculate total player points
  print player character/history
  game save/load code
  add player to scores
  various other features and functions

<PERSON><PERSON> <PERSON><PERSON> (Public Domain)
  MSDOS Moria port
  reduced map display
  16 bit integers

Christopher <PERSON> [-CJS-] (GPL v2)
  recall
  options
  inventory
  running code

<PERSON>cCauley (GPL v2)
  Macintosh Moria port for MPW C

Stephen A. Jacobs (GPL v2)
  Atari ST Moria port for MW C

William Setzer (GPL v2)
  object naming code

Dan Bernstein [-DJB-] (Public Domain)
  "no need to bargain" functionality
  update bargin info
  UNIX hangup signal fix
  many bug fixes

Corey Gehman (GPL v2)
  Amiga Moria port

Ralph Waters (GPL v2)
  VMS support code
  IBM-PC Turbo C bug fixes

Joshua Delahunty (GPL v2)
  VMS support code

Todd Pierzina (GPL v2)
  VMS support code

Joseph Hall (Public Domain)
  line of sight code
  monster compiler

Eric Vaitl (GPL v2)
  PC-Curses replacement for Turbo-C

Scott Kolodzieski (GPL v2)
  Atari ST port for GNU C

Hildo Biersma (Public Domain)
  Atari ST port for Turbo C

Ben Schreiber (GPL v2)
  Macintosh port for Think C

Carlton Hommel (Public Domain)
  the 'print items' utility

Wayne Schlitt (GPL v2)
  the 'calculate hits' utility

Brian W. Johnson (GPL v2)
  Linux support

Ronald Cook (Public Domain)
  lattice C support

Eric Bazin (Public Domain)
  merge monster memories

Berthold Gunreben (GPL v2)
  HP-UX support

Andrew Chernov (Public Domain)
  386 BSD support

Harry Johnston (GPL v2)
  specify an item by its numeric inscription

Rene Weber [-RJW-] (GPL v2)
  compiling and doc updates for Umoria v5.6


The following signature is currently unknown but listed here for completeness.

-RGM- (contributed > v4.8.5.2 and <= 4.8.7 )
  created get_all_stats() for character class loop
