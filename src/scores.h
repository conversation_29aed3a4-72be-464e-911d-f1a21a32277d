// Copyright (c) 1981-86 <PERSON>
// Copyright (c) 1987-94 <PERSON>
//
// SPDX-License-Identifier: GPL-3.0-or-later

#pragma once

#include <cstdint>

// HighScore_t is a score object used for saving to the high score file
// This structure is 64 bytes in size
typedef struct {
    int32_t points;
    int32_t birth_date;
    int16_t uid;
    int16_t mhp;
    int16_t chp;
    uint8_t dungeon_depth;
    uint8_t level;
    uint8_t deepest_dungeon_depth;
    uint8_t gender;
    uint8_t race;
    uint8_t character_class;
    char name[PLAYER_NAME_SIZE];
    char died_from[25];
} HighScore_t;

// Number of entries allowed in the score file.
constexpr uint16_t MAX_HIGH_SCORE_ENTRIES = 1000;

extern FILE *highscore_fp;

// TODO: these are implemented in `game_save.cpp` so need moving.
void saveHighScore(HighScore_t const &score);
void readHighScore(HighScore_t &score);

void recordNewHighScore();
void showScoresScreen();
int32_t playerCalculateTotalPoints();
