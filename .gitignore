.idea

# CMake
Makefile
CMakeCache.txt
CMakeFiles
CMakeScripts
cmake_install.cmake
install_manifest.txt
CPackConfig.cmake
CPackSourceConfig.cmake
CTestTestfile.cmake
cmake-build-debug
cmake-build-release
_CPack_Packages

# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf

# Linker output
*.ilk
*.map
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
modules.order
Module.symvers
Mkfile.old
dkms.conf

*.cbp

# build directories
umoria.xcodeproj
nbproject
umoria

compile_commands.json
*.gz
*.zip

.DS_Store
